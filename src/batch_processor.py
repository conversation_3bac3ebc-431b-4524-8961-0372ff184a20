"""Batch processing capabilities for OCR pipeline with comprehensive error handling."""

import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
from concurrent.futures import ThreadPoolExecutor
import tempfile
import uuid

from .ocr_processor import GeminiOCRProcessor, OCRResult
from .pii_detector import PIIDete<PERSON>, PIIMatch
from .pdf_obfuscator import PDFObfuscator, ObfuscationMethod
from .config import get_processing_config, get_file_config


logger = logging.getLogger(__name__)


class ProcessingStatus(Enum):
    """Status of batch processing operations."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRYING = "retrying"


@dataclass
class ProcessingResult:
    """Result of processing a single file."""
    
    file_path: Path
    status: ProcessingStatus
    ocr_result: Optional[OCRResult] = None
    pii_matches: List[PIIMatch] = field(default_factory=list)
    obfuscation_result: Optional[Dict[str, Any]] = None
    
    # Timing information
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    processing_time: float = 0.0
    
    # Error information
    error_message: Optional[str] = None
    retry_count: int = 0
    
    # File metadata
    file_size: int = 0
    output_path: Optional[Path] = None
    
    def mark_completed(self, processing_time: float = None):
        """Mark the processing as completed."""
        self.status = ProcessingStatus.COMPLETED
        self.end_time = time.time()
        if processing_time is not None:
            self.processing_time = processing_time
        else:
            self.processing_time = self.end_time - self.start_time
    
    def mark_failed(self, error_message: str):
        """Mark the processing as failed."""
        self.status = ProcessingStatus.FAILED
        self.end_time = time.time()
        self.processing_time = self.end_time - self.start_time
        self.error_message = error_message
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
        "file_path": str(self.file_path),
            "status": self.status.value,
            "ocr_result": self.ocr_result.dict() if self.ocr_result else None,
            "pii_matches": len(self.pii_matches),
            "obfuscation_success": self.obfuscation_result.get("success", False) if self.obfuscation_result else None,
            "transaction_id": self.obfuscation_result.get("transaction_id") if self.obfuscation_result else None,
            "processing_time": self.processing_time,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "file_size": self.file_size,
            "output_path": str(self.output_path) if self.output_path else None
        }


@dataclass
class BatchProcessingStats:
    """Statistics for batch processing operations."""
    
    total_files: int = 0
    completed_files: int = 0
    failed_files: int = 0
    skipped_files: int = 0
    
    total_processing_time: float = 0.0
    average_processing_time: float = 0.0
    
    total_pii_detected: int = 0
    total_pii_obfuscated: int = 0
    
    # Error breakdown
    error_types: Dict[str, int] = field(default_factory=dict)
    
    def update_from_result(self, result: ProcessingResult):
        """Update statistics from a processing result."""
        if result.status == ProcessingStatus.COMPLETED:
            self.completed_files += 1
            self.total_processing_time += result.processing_time
            
            if result.pii_matches:
                self.total_pii_detected += len(result.pii_matches)
            
            if result.obfuscation_result and result.obfuscation_result.get("success"):
                self.total_pii_obfuscated += result.obfuscation_result.get("pii_items_obfuscated", 0)
                
        elif result.status == ProcessingStatus.FAILED:
            self.failed_files += 1
            
            # Track error types
            error_type = self._categorize_error(result.error_message or "Unknown error")
            self.error_types[error_type] = self.error_types.get(error_type, 0) + 1
            
        elif result.status == ProcessingStatus.SKIPPED:
            self.skipped_files += 1
        
        # Update averages
        if self.completed_files > 0:
            self.average_processing_time = self.total_processing_time / self.completed_files
    
    def _categorize_error(self, error_message: str) -> str:
        """Categorize error message into type."""
        error_msg_lower = error_message.lower()
        
        if "api" in error_msg_lower or "quota" in error_msg_lower:
            return "API Error"
        elif "file" in error_msg_lower or "path" in error_msg_lower:
            return "File Error"
        elif "pdf" in error_msg_lower or "format" in error_msg_lower:
            return "PDF Format Error"
        elif "timeout" in error_msg_lower or "connection" in error_msg_lower:
            return "Network Error"
        elif "memory" in error_msg_lower or "size" in error_msg_lower:
            return "Resource Error"
        else:
            return "Unknown Error"


class ProgressCallback:
    """Callback interface for progress reporting."""
    
    def on_file_started(self, file_path: Path, current: int, total: int):
        """Called when processing starts for a file."""
        pass
    
    def on_file_completed(self, result: ProcessingResult, current: int, total: int):
        """Called when processing completes for a file."""
        pass
    
    def on_batch_completed(self, stats: BatchProcessingStats):
        """Called when batch processing completes."""
        pass


class ConsoleProgressCallback(ProgressCallback):
    """Console-based progress reporting."""
    
    def on_file_started(self, file_path: Path, current: int, total: int):
        """Print progress to console."""
        logger.info(f"Processing file {current}/{total}: {file_path.name}")
    
    def on_file_completed(self, result: ProcessingResult, current: int, total: int):
        """Print completion status."""
        status_symbol = "✓" if result.status == ProcessingStatus.COMPLETED else "✗"
        logger.info(
            f"{status_symbol} {result.file_path.name} - "
            f"{result.status.value} ({result.processing_time:.2f}s)"
        )
    
    def on_batch_completed(self, stats: BatchProcessingStats):
        """Print batch completion summary."""
        logger.info(
            f"Batch completed: {stats.completed_files}/{stats.total_files} files processed "
            f"({stats.failed_files} failed, {stats.skipped_files} skipped)"
        )


class BatchProcessor:
    """Batch processor for OCR pipeline with comprehensive error handling."""
    
    def __init__(
        self,
        ocr_processor: Optional[GeminiOCRProcessor] = None,
        pii_detector: Optional[PIIDetector] = None,
        pdf_obfuscator: Optional[PDFObfuscator] = None,
        progress_callback: Optional[ProgressCallback] = None
    ):
        """Initialize batch processor.
        
        Args:
            ocr_processor: OCR processor instance
            pii_detector: PII detector instance
            pdf_obfuscator: PDF obfuscator instance
            progress_callback: Progress reporting callback
        """
        self.ocr_processor = ocr_processor or GeminiOCRProcessor()
        self.pii_detector = pii_detector or PIIDetector()
        self.pdf_obfuscator = pdf_obfuscator or PDFObfuscator()
        self.progress_callback = progress_callback or ConsoleProgressCallback()
        
        # Configuration
        self.processing_config = get_processing_config()
        self.file_config = get_file_config()
        
        # Rate limiting
        self.semaphore = asyncio.Semaphore(self.processing_config["max_concurrent"])
        
        logger.info("Batch processor initialized")
    
    async def process_batch(
        self,
        input_paths: List[Path],
        output_dir: Path,
        enable_obfuscation: bool = True,
        save_report: bool = True
    ) -> Dict[str, Any]:
        """Process a batch of PDF files.
        
        Args:
            input_paths: List of input PDF paths
            output_dir: Output directory for processed files
            enable_obfuscation: Whether to enable PDF obfuscation
            save_report: Whether to save processing report
            
        Returns:
            Batch processing results and statistics
        """
        start_time = time.time()
        
        # Validate inputs
        valid_paths = self._validate_input_files(input_paths)
        if not valid_paths:
            raise ValueError("No valid input files found")
        
        # Ensure output directory exists
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize tracking
        stats = BatchProcessingStats(total_files=len(valid_paths))
        results = []
        
        logger.info(f"Starting batch processing of {len(valid_paths)} files")
        
        try:
            # Process files with concurrency control
            semaphore_tasks = []
            for i, file_path in enumerate(valid_paths):
                task = self._process_single_file_with_semaphore(
                    file_path, output_dir, enable_obfuscation, i + 1, len(valid_paths)
                )
                semaphore_tasks.append(task)
            
            # Wait for all tasks to complete
            processing_results = await asyncio.gather(*semaphore_tasks, return_exceptions=True)
            
            # Process results
            for i, result in enumerate(processing_results):
                if isinstance(result, Exception):
                    # Handle task exceptions
                    error_result = ProcessingResult(
                        file_path=valid_paths[i],
                        status=ProcessingStatus.FAILED
                    )
                    error_result.mark_failed(str(result))
                    results.append(error_result)
                else:
                    results.append(result)
                
                # Update statistics
                stats.update_from_result(results[-1])
            
            total_time = time.time() - start_time
            
            # Prepare final report
            batch_report = {
                "batch_stats": stats,
                "processing_results": [r.to_dict() for r in results],
                "total_batch_time": total_time,
                "configuration": {
                    "max_concurrent": self.processing_config["max_concurrent"],
                    "batch_size": self.processing_config["batch_size"],
                    "confidence_threshold": self.pii_detector.confidence_threshold,
                    "obfuscation_method": self.pdf_obfuscator.method.value,
                    "enable_obfuscation": enable_obfuscation
                },
                "timestamp": time.time()
            }
            
            # Save report if requested
            if save_report:
                report_path = output_dir / f"batch_report_{int(time.time())}.json"
                with open(report_path, 'w', encoding='utf-8') as f:
                    json.dump(batch_report, f, indent=2, ensure_ascii=False, default=str)
                logger.info(f"Batch report saved to: {report_path}")
            
            # Notify callback
            self.progress_callback.on_batch_completed(stats)
            
            logger.info(
                f"Batch processing completed in {total_time:.2f}s: "
                f"{stats.completed_files} success, {stats.failed_files} failed"
            )
            
            return batch_report
            
        except Exception as e:
            logger.error(f"Batch processing failed: {str(e)}")
            raise
    
    async def _process_single_file_with_semaphore(
        self,
        file_path: Path,
        output_dir: Path,
        enable_obfuscation: bool,
        current: int,
        total: int
    ) -> ProcessingResult:
        """Process a single file with semaphore control."""
        async with self.semaphore:
            return await self._process_single_file(
                file_path, output_dir, enable_obfuscation, current, total
            )
    
    async def _process_single_file(
        self,
        file_path: Path,
        output_dir: Path,
        enable_obfuscation: bool,
        current: int,
        total: int
    ) -> ProcessingResult:
        """Process a single PDF file through the complete pipeline.
        
        Args:
            file_path: Path to input PDF file
            output_dir: Output directory
            enable_obfuscation: Whether to perform obfuscation
            current: Current file number
            total: Total number of files
            
        Returns:
            ProcessingResult object
        """
        result = ProcessingResult(
            file_path=file_path,
            status=ProcessingStatus.PROCESSING,
            file_size=file_path.stat().st_size
        )
        
        # Notify callback
        self.progress_callback.on_file_started(file_path, current, total)
        
        try:
            # Step 1: Enhanced OCR Processing with Transaction Recording
            logger.debug(f"Starting enhanced OCR with ULTRA THINK for: {file_path}")
            ocr_result = await self.ocr_processor.process_pdf_with_retry(file_path)
            result.ocr_result = ocr_result
            
            # Record transaction in database if processor supports it
            if hasattr(self.ocr_processor, 'record_processing_transaction'):
                transaction_id = await self.ocr_processor.record_processing_transaction(
                    file_path, ocr_result
                )
                if transaction_id:
                    result.obfuscation_result = result.obfuscation_result or {}
                    result.obfuscation_result['transaction_id'] = transaction_id
                    logger.debug(f"Recorded transaction: {transaction_id}")
            
            if ocr_result.errors:
                result.mark_failed(f"OCR failed: {'; '.join(ocr_result.errors)}")
                self.progress_callback.on_file_completed(result, current, total)
                return result
            
            # Step 2: PII Detection
            logger.debug(f"Detecting PII for: {file_path}")
            pii_matches = self.pii_detector.detect_pii(ocr_result.full_text)
            result.pii_matches = pii_matches
            
            # Step 3: PDF Obfuscation (if enabled and PII found)
            if enable_obfuscation and pii_matches:
                output_path = output_dir / f"obfuscated_{file_path.name}"
                result.output_path = output_path
                
                logger.debug(f"Obfuscating PDF: {file_path}")
                obfuscation_result = await self.pdf_obfuscator.obfuscate_pdf(
                    file_path, output_path, pii_matches
                )
                result.obfuscation_result = obfuscation_result
                
                if not obfuscation_result.get("success", False):
                    result.mark_failed(f"Obfuscation failed: {obfuscation_result.get('error', 'Unknown error')}")
                    self.progress_callback.on_file_completed(result, current, total)
                    return result
            
            # Mark as completed
            result.mark_completed()
            
        except Exception as e:
            logger.error(f"Processing failed for {file_path}: {str(e)}")
            result.mark_failed(str(e))
        
        # Notify callback
        self.progress_callback.on_file_completed(result, current, total)
        
        return result
    
    def _validate_input_files(self, input_paths: List[Path]) -> List[Path]:
        """Validate input files and filter valid ones.
        
        Args:
            input_paths: List of input paths to validate
            
        Returns:
            List of valid PDF file paths
        """
        valid_paths = []
        max_size_bytes = self.file_config["max_size_mb"] * 1024 * 1024
        
        for path in input_paths:
            try:
                if not path.exists():
                    logger.warning(f"File not found: {path}")
                    continue
                
                if not path.is_file():
                    logger.warning(f"Not a file: {path}")
                    continue
                
                if path.suffix.lower() not in ['.pdf']:
                    logger.warning(f"Unsupported file format: {path}")
                    continue
                
                file_size = path.stat().st_size
                if file_size > max_size_bytes:
                    logger.warning(f"File too large ({file_size / 1024 / 1024:.1f}MB): {path}")
                    continue
                
                if file_size == 0:
                    logger.warning(f"Empty file: {path}")
                    continue
                
                valid_paths.append(path)
                
            except Exception as e:
                logger.warning(f"Error validating file {path}: {str(e)}")
        
        logger.info(f"Validated {len(valid_paths)}/{len(input_paths)} files")
        return valid_paths
    
    async def process_directory(
        self,
        input_dir: Path,
        output_dir: Path,
        pattern: str = "*.pdf",
        recursive: bool = True,
        enable_obfuscation: bool = True
    ) -> Dict[str, Any]:
        """Process all PDF files in a directory.
        
        Args:
            input_dir: Input directory to scan
            output_dir: Output directory for processed files
            pattern: File pattern for matching (default: "*.pdf")
            recursive: Whether to scan subdirectories
            enable_obfuscation: Whether to enable PDF obfuscation
            
        Returns:
            Batch processing results
        """
        if not input_dir.exists() or not input_dir.is_dir():
            raise ValueError(f"Input directory does not exist: {input_dir}")
        
        # Find all PDF files
        if recursive:
            pdf_files = list(input_dir.rglob(pattern))
        else:
            pdf_files = list(input_dir.glob(pattern))
        
        if not pdf_files:
            raise ValueError(f"No PDF files found in {input_dir}")
        
        logger.info(f"Found {len(pdf_files)} PDF files in {input_dir}")
        
        return await self.process_batch(
            pdf_files, output_dir, enable_obfuscation=enable_obfuscation
        )


    async def process_multiple_uploads(
        self,
        pdf_files: List[Tuple[Path, bytes]],  # (filename, file_content) pairs
        output_dir: Path,
        organization_id: Optional[str] = None,
        enable_obfuscation: bool = True,
        async_processing: bool = True
    ) -> Dict[str, Any]:
        """Process multiple uploaded PDF files with async processing capability.
        
        Args:
            pdf_files: List of (Path, bytes) tuples for uploaded files
            output_dir: Output directory for processed files
            organization_id: Organization ID for database recording
            enable_obfuscation: Whether to enable PDF obfuscation
            async_processing: If True, returns immediately with job ID for status tracking
            
        Returns:
            Processing results or job tracking information
        """
        import tempfile
        import asyncio
        from concurrent.futures import ThreadPoolExecutor
        
        # Create temporary files for uploaded content
        temp_files = []
        try:
            for filename, content in pdf_files:
                temp_file = tempfile.NamedTemporaryFile(
                    suffix=f"_{filename.name}", 
                    delete=False
                )
                temp_file.write(content)
                temp_file.close()
                temp_files.append(Path(temp_file.name))
            
            if async_processing:
                # Start processing in background and return job ID
                job_id = str(uuid.uuid4())
                
                async def background_processing():
                    try:
                        # Enhanced OCR processor with database integration
                        if hasattr(self.ocr_processor, 'process_pdf_batch'):
                            results = await self.ocr_processor.process_pdf_batch(
                                temp_files, organization_id
                            )
                            # Store results for later retrieval (in production, use Redis/database)
                            logger.info(f"Background processing completed for job {job_id}")
                        else:
                            # Fallback to regular batch processing
                            await self.process_batch(
                                temp_files, output_dir, enable_obfuscation
                            )
                    except Exception as e:
                        logger.error(f"Background processing failed for job {job_id}: {e}")
                    finally:
                        # Cleanup temporary files
                        for temp_file in temp_files:
                            try:
                                temp_file.unlink()
                            except Exception as e:
                                logger.warning(f"Failed to cleanup {temp_file}: {e}")
                
                # Start background task
                asyncio.create_task(background_processing())
                
                return {
                    "job_id": job_id,
                    "status": "processing",
                    "total_files": len(pdf_files),
                    "estimated_completion_time": len(pdf_files) * 30,  # 30 seconds per file estimate
                    "message": "Processing started in background. Use job_id to check status."
                }
            else:
                # Synchronous processing
                return await self.process_batch(
                    temp_files, output_dir, enable_obfuscation
                )
                
        except Exception as e:
            logger.error(f"Multiple upload processing failed: {str(e)}")
            raise
        finally:
            # Cleanup temporary files if not handled by background task
            if not async_processing:
                for temp_file in temp_files:
                    try:
                        temp_file.unlink()
                    except Exception as e:
                        logger.warning(f"Failed to cleanup {temp_file}: {e}")


# Utility functions for batch processing
async def process_pdfs_from_directory(
    input_dir: str,
    output_dir: str,
    **kwargs
) -> Dict[str, Any]:
    """Convenience function to process PDFs from a directory.
    
    Args:
        input_dir: Input directory path
        output_dir: Output directory path
        **kwargs: Additional arguments for BatchProcessor
        
    Returns:
        Processing results
    """
    processor = BatchProcessor()
    
    return await processor.process_directory(
        Path(input_dir),
        Path(output_dir),
        **kwargs
    )


async def process_pdf_files(
    file_paths: List[str],
    output_dir: str,
    **kwargs
) -> Dict[str, Any]:
    """Convenience function to process specific PDF files.
    
    Args:
        file_paths: List of PDF file paths
        output_dir: Output directory path
        **kwargs: Additional arguments for BatchProcessor
        
    Returns:
        Processing results
    """
    processor = BatchProcessor()
    
    return await processor.process_batch(
        [Path(p) for p in file_paths],
        Path(output_dir),
        **kwargs
    )