"""Configuration management for ChromoForge OCR pipeline."""

import os
from pathlib import Path
from typing import Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with validation."""
    
    # Google Gemini API Configuration
    google_api_key: str = Field(..., env="GOOGLE_API_KEY")
    
    # Supabase Configuration
    supabase_url: str = Field(..., env="NEXT_PUBLIC_SUPABASE_URL")
    supabase_anon_key: str = Field(..., env="NEXT_PUBLIC_SUPABASE_ANON_KEY")
    supabase_service_key: str = Field(..., env="SUPABASE_SERVICE_ROLE_KEY")
    
    # OCR Processing Configuration
    max_retries: int = Field(default=3, env="MAX_RETRIES", ge=1, le=10)
    retry_delay: float = Field(default=1.0, env="RETRY_DELAY", ge=0.1, le=10.0)
    batch_size: int = Field(default=5, env="BATCH_SIZE", ge=1, le=50)
    max_concurrent_requests: int = Field(
        default=10, env="MAX_CONCURRENT_REQUESTS", ge=1, le=100
    )
    
    # File Processing Configuration
    max_file_size_mb: int = Field(default=50, env="MAX_FILE_SIZE_MB", ge=1, le=200)
    supported_formats: str = Field(default="pdf", env="SUPPORTED_FORMATS")
    output_dir: Path = Field(default=Path("./processed"), env="OUTPUT_DIR")
    temp_dir: Path = Field(default=Path("./temp"), env="TEMP_DIR")
    
    # PII Detection Configuration
    confidence_threshold: float = Field(
        default=0.7, env="CONFIDENCE_THRESHOLD", ge=0.0, le=1.0
    )
    enable_coordinate_tracking: bool = Field(
        default=True, env="ENABLE_COORDINATE_TRACKING"
    )
    obfuscation_method: str = Field(
        default="black_box", 
        env="OBFUSCATION_METHOD",
        pattern=r"^(black_box|blur|redact)$"
    )
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")
    
    # Enhanced Gemini 2.5 Pro Configuration
    gemini_model: str = Field(default="gemini-2.5-pro", env="GEMINI_MODEL")
    gemini_temperature: float = Field(default=0.1, env="GEMINI_TEMPERATURE", ge=0.0, le=2.0)
    gemini_max_tokens: int = Field(default=8192, env="GEMINI_MAX_TOKENS", ge=1024, le=32768)
    
    # Enhanced Thinking Configuration
    gemini_thinking_budget: int = Field(default=-1, env="GEMINI_THINKING_BUDGET", ge=-1)
    enable_ultra_think: bool = Field(default=True, env="ENABLE_ULTRA_THINK")
    enable_google_search: bool = Field(default=True, env="ENABLE_GOOGLE_SEARCH")
    enable_url_context: bool = Field(default=True, env="ENABLE_URL_CONTEXT")
    
    # Database Transaction Recording
    enable_database_recording: bool = Field(default=True, env="ENABLE_DATABASE_RECORDING")
    default_organization_id: Optional[str] = Field(default=None, env="DEFAULT_ORGANIZATION_ID")
    
    # Enhanced Thai OCR Settings
    thai_cross_reference: bool = Field(default=True, env="THAI_CROSS_REFERENCE")
    contextual_name_mapping: bool = Field(default=True, env="CONTEXTUAL_NAME_MAPPING")
    medical_field_extraction: bool = Field(default=True, env="MEDICAL_FIELD_EXTRACTION")
    
    @field_validator("output_dir", "temp_dir", mode="before")
    @classmethod
    def create_directories(cls, v: Path) -> Path:
        """Create directories if they don't exist."""
        if isinstance(v, str):
            v = Path(v)
        v.mkdir(parents=True, exist_ok=True)
        return v
    
    @field_validator("supported_formats")
    @classmethod
    def validate_formats(cls, v: str) -> list[str]:
        """Parse and validate supported file formats."""
        formats = [fmt.strip().lower() for fmt in v.split(",")]
        valid_formats = {"pdf", "jpg", "jpeg", "png", "tiff"}
        
        for fmt in formats:
            if fmt not in valid_formats:
                raise ValueError(f"Unsupported format: {fmt}")
        
        return formats
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False
    }


# Global settings instance
settings = Settings()


def get_gemini_config() -> dict:
    """Get enhanced Gemini API configuration."""
    return {
        "api_key": settings.google_api_key,
        "model": settings.gemini_model,
        "temperature": settings.gemini_temperature,
        "max_tokens": settings.gemini_max_tokens,
        "thinking_budget": settings.gemini_thinking_budget,
        "enable_ultra_think": settings.enable_ultra_think,
        "enable_google_search": settings.enable_google_search,
        "enable_url_context": settings.enable_url_context,
        "thai_cross_reference": settings.thai_cross_reference,
        "contextual_name_mapping": settings.contextual_name_mapping,
        "medical_field_extraction": settings.medical_field_extraction,
    }


def get_processing_config() -> dict:
    """Get OCR processing configuration."""
    return {
        "max_retries": settings.max_retries,
        "retry_delay": settings.retry_delay,
        "batch_size": settings.batch_size,
        "max_concurrent": settings.max_concurrent_requests,
        "confidence_threshold": settings.confidence_threshold,
    }


def get_file_config() -> dict:
    """Get file processing configuration."""
    return {
        "max_size_mb": settings.max_file_size_mb,
        "supported_formats": settings.supported_formats,
        "output_dir": settings.output_dir,
        "temp_dir": settings.temp_dir,
        "obfuscation_method": settings.obfuscation_method,
    }


def get_database_config() -> dict:
    """Get database configuration for transaction recording."""
    return {
        "supabase_url": settings.supabase_url,
        "supabase_anon_key": settings.supabase_anon_key,
        "supabase_service_key": settings.supabase_service_key,
        "enable_recording": settings.enable_database_recording,
        "default_organization_id": settings.default_organization_id,
    }