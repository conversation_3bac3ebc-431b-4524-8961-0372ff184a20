"""Main application entry point for ChromoForge OCR pipeline.

This module provides comprehensive error handling and recovery mechanisms:
- Graceful degradation when services are unavailable
- Circuit breaker patterns for external API calls
- Retry logic with exponential backoff
- Recovery strategies for common failure scenarios
- HIPAA-compliant error logging (no sensitive data in logs)
"""

import asyncio
import argparse
import logging
import sys
import time
from pathlib import Path
from typing import Optional, Dict, Any
import json
from functools import wraps

from .logging_config import setup_logging, OCRLogger
from .batch_processor import BatchProcessor, process_pdfs_from_directory, process_pdf_files
from .ocr_processor import GeminiOCRProcessor
from .pii_detector import PIIDetector
from .pdf_obfuscator import PDFObfuscator, ObfuscationMethod
from .config import settings
from .exceptions import (
    ChromoForgeBaseException, OCRProcessingException, FileAccessException,
    ConfigurationException, BatchProcessingException, handle_exception
)


# Set up logging before other imports
setup_logging()
logger = logging.getLogger(__name__)
ocr_logger = OCRLogger(__name__)


def circuit_breaker(failure_threshold: int = 5, timeout: int = 60):
    """Circuit breaker decorator for preventing cascading failures.
    
    Args:
        failure_threshold: Number of failures before opening circuit
        timeout: Seconds to wait before attempting reset
    """
    def decorator(func):
        func._failure_count = 0
        func._last_failure_time = 0
        func._circuit_open = False
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Check if circuit is open
            if func._circuit_open:
                if time.time() - func._last_failure_time < timeout:
                    raise OCRProcessingException(
                        message="Circuit breaker is open - service temporarily unavailable",
                        error_code="CIRCUIT_OPEN",
                        retry_able=True,
                        user_message="Service is temporarily unavailable. Please try again later."
                    )
                else:
                    # Attempt to reset circuit
                    func._circuit_open = False
                    func._failure_count = 0
                    logger.info(f"Circuit breaker reset for {func.__name__}")
            
            try:
                result = await func(*args, **kwargs)
                # Reset failure count on success
                func._failure_count = 0
                return result
            except Exception as e:
                func._failure_count += 1
                func._last_failure_time = time.time()
                
                if func._failure_count >= failure_threshold:
                    func._circuit_open = True
                    logger.error(
                        f"Circuit breaker opened for {func.__name__} after {func._failure_count} failures"
                    )
                
                raise e
        
        return wrapper
    return decorator


class ChromoForgeApp:
    """Main application class for ChromoForge OCR pipeline.
    
    Provides comprehensive error handling and recovery mechanisms:
    - Circuit breaker patterns for external services
    - Graceful degradation when components fail
    - Retry logic with exponential backoff
    - HIPAA-compliant error logging
    - Recovery strategies for common failure scenarios
    """
    
    def __init__(self, user_id: Optional[str] = None, session_id: Optional[str] = None):
        """Initialize the enhanced ChromoForge application with error handling.
        
        Args:
            user_id: User ID for audit logging
            session_id: Session ID for tracking
            
        Raises:
            ConfigurationException: If initialization fails due to configuration issues
            OCRProcessingException: If OCR processor initialization fails
        """
        self.user_id = user_id
        self.session_id = session_id
        self._initialization_errors = []
        
        try:
            # Initialize components with error handling
            self._initialize_components()
            
            logger.info(
                "Enhanced ChromoForge OCR pipeline initialized with comprehensive error handling",
                extra={"user_id": user_id, "session_id": session_id}
            )
            
        except Exception as e:
            error_msg = f"Failed to initialize ChromoForge application: {str(e)}"
            logger.error(error_msg, extra={"user_id": user_id, "session_id": session_id})
            raise ConfigurationException(
                message=error_msg,
                config_section="application_initialization",
                context={"user_id": user_id, "session_id": session_id},
                original_exception=e
            )
    
    def _initialize_components(self) -> None:
        """Initialize application components with error handling and fallbacks.
        
        Raises:
            ConfigurationException: If critical components cannot be initialized
        """
        # Initialize OCR processor with error handling
        try:
            self.ocr_processor = GeminiOCRProcessor(
                user_id=self.user_id, 
                session_id=self.session_id
            )
            logger.info("OCR processor initialized successfully")
        except Exception as e:
            self._initialization_errors.append(f"OCR processor: {str(e)}")
            logger.error(f"Failed to initialize OCR processor: {str(e)}")
            # OCR processor is critical - re-raise
            raise
        
        # Initialize PII detector with error handling
        try:
            self.pii_detector = PIIDetector(
                confidence_threshold=settings.confidence_threshold
            )
            logger.info("PII detector initialized successfully")
        except Exception as e:
            self._initialization_errors.append(f"PII detector: {str(e)}")
            logger.error(f"Failed to initialize PII detector: {str(e)}")
            # Use fallback PII detector with basic patterns
            try:
                self.pii_detector = PIIDetector(confidence_threshold=0.5)
                logger.warning("Using fallback PII detector with reduced confidence threshold")
            except Exception as fallback_error:
                logger.critical(f"Failed to initialize fallback PII detector: {str(fallback_error)}")
                # PII detection is critical for medical documents - re-raise original error
                raise e
        
        # Initialize PDF obfuscator with error handling
        try:
            self.pdf_obfuscator = PDFObfuscator(
                method=ObfuscationMethod(settings.obfuscation_method)
            )
            logger.info("PDF obfuscator initialized successfully")
        except Exception as e:
            self._initialization_errors.append(f"PDF obfuscator: {str(e)}")
            logger.error(f"Failed to initialize PDF obfuscator: {str(e)}")
            # Use fallback obfuscation method
            try:
                self.pdf_obfuscator = PDFObfuscator(method=ObfuscationMethod.BLACK_BOX)
                logger.warning("Using fallback black box obfuscation method")
            except Exception as fallback_error:
                logger.critical(f"Failed to initialize fallback PDF obfuscator: {str(fallback_error)}")
                # PDF obfuscation is critical for PII protection - re-raise original error
                raise e
        
        # Log any non-critical initialization errors
        if self._initialization_errors:
            logger.warning(
                f"Some components had initialization issues: {'; '.join(self._initialization_errors)}"
            )
    
    async def process_single_file(
        self, 
        input_path: Path, 
        output_dir: Path,
        enable_obfuscation: bool = True
    ) -> dict:
        """Process a single PDF file.
        
        Args:
            input_path: Path to input PDF file
            output_dir: Output directory for results
            enable_obfuscation: Whether to perform PII obfuscation
            
        Returns:
            Processing results dictionary
        """
        logger.info(f"Processing single file: {input_path}")
        ocr_logger.log_ocr_start(str(input_path), input_path.stat().st_size)
        
        try:
            # Ensure output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Step 1: Enhanced OCR Processing with ULTRA THINK
            ocr_result = await self.ocr_processor.process_pdf_with_retry(input_path)
            
            # Record transaction in database if enabled
            if hasattr(self.ocr_processor, 'record_processing_transaction'):
                transaction_id = await self.ocr_processor.record_processing_transaction(
                    input_path, ocr_result
                )
                if transaction_id:
                    logger.info(f"Recorded transaction: {transaction_id}")
            
            if ocr_result.errors:
                ocr_logger.log_ocr_error(str(input_path), "; ".join(ocr_result.errors))
                return {
                    "success": False,
                    "error": "OCR processing failed",
                    "details": ocr_result.errors
                }
            
            # Step 2: PII Detection
            pii_matches = self.pii_detector.detect_pii(ocr_result.full_text)
            
            # Log PII detection results
            pii_types = {}
            for match in pii_matches:
                pii_type = match.pii_type.value
                pii_types[pii_type] = pii_types.get(pii_type, 0) + 1
            
            ocr_logger.log_pii_detection(str(input_path), pii_types, len(pii_matches))
            
            # Step 3: Save OCR results
            results_path = output_dir / f"{input_path.stem}_ocr_results.json"
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "file_path": str(input_path),
                    "ocr_result": ocr_result.dict(),
                    "pii_matches": [
                        {
                            "type": match.pii_type.value,
                            "text": match.text,
                            "confidence": match.confidence,
                            "line_number": match.line_number,
                            "context": match.context
                        }
                        for match in pii_matches
                    ]
                }, f, indent=2, ensure_ascii=False, default=str)
            
            # Step 4: PDF Obfuscation (if enabled and PII found)
            obfuscation_result = None
            if enable_obfuscation and pii_matches:
                output_path = output_dir / f"obfuscated_{input_path.name}"
                obfuscation_result = await self.pdf_obfuscator.obfuscate_pdf(
                    input_path, output_path, pii_matches
                )
                
                if obfuscation_result.get("success"):
                    ocr_logger.log_obfuscation_complete(
                        str(input_path),
                        str(output_path),
                        obfuscation_result.get("pii_items_obfuscated", 0),
                        self.pdf_obfuscator.method.value
                    )
            
            # Log completion
            ocr_logger.log_ocr_complete(
                str(input_path),
                ocr_result.processing_time,
                ocr_result.confidence_score,
                len(pii_matches)
            )
            
            return {
                "success": True,
                "file_path": str(input_path),
                "results_path": str(results_path),
                "ocr_result": ocr_result.dict(),
                "pii_matches_count": len(pii_matches),
                "obfuscation_result": obfuscation_result,
                "processing_time": ocr_result.processing_time
            }
            
        except Exception as e:
            logger.error(f"Failed to process {input_path}: {str(e)}")
            ocr_logger.log_ocr_error(str(input_path), str(e))
            return {
                "success": False,
                "error": str(e),
                "file_path": str(input_path)
            }
    
    async def process_batch(
        self,
        input_paths: list[Path],
        output_dir: Path,
        enable_obfuscation: bool = True
    ) -> dict:
        """Process multiple PDF files in batch.
        
        Args:
            input_paths: List of input PDF paths
            output_dir: Output directory for results
            enable_obfuscation: Whether to perform PII obfuscation
            
        Returns:
            Batch processing results
        """
        logger.info(f"Processing batch of {len(input_paths)} files")
        
        batch_processor = BatchProcessor(
            self.ocr_processor,
            self.pii_detector,
            self.pdf_obfuscator
        )
        
        results = await batch_processor.process_batch(
            input_paths,
            output_dir,
            enable_obfuscation=enable_obfuscation
        )
        
        # Log batch statistics
        stats = results["batch_stats"]
        ocr_logger.log_batch_stats(
            stats.total_files,
            stats.completed_files,
            stats.failed_files,
            stats.average_processing_time
        )
        
        return results
    
    async def process_directory(
        self,
        input_dir: Path,
        output_dir: Path,
        pattern: str = "*.pdf",
        recursive: bool = True,
        enable_obfuscation: bool = True
    ) -> dict:
        """Process all PDF files in a directory.
        
        Args:
            input_dir: Input directory to scan
            output_dir: Output directory for results
            pattern: File pattern for matching
            recursive: Whether to scan subdirectories
            enable_obfuscation: Whether to perform PII obfuscation
            
        Returns:
            Processing results
        """
        logger.info(f"Processing directory: {input_dir}")
        
        batch_processor = BatchProcessor(
            self.ocr_processor,
            self.pii_detector,
            self.pdf_obfuscator
        )
        
        return await batch_processor.process_directory(
            input_dir,
            output_dir,
            pattern=pattern,
            recursive=recursive,
            enable_obfuscation=enable_obfuscation
        )


async def main():
    """Main entry point for the application."""
    parser = argparse.ArgumentParser(
        description="ChromoForge OCR Pipeline - Process medical PDFs with PII detection and obfuscation"
    )
    
    # Input/Output options
    parser.add_argument(
        "--input", "-i",
        type=str,
        required=True,
        help="Input PDF file or directory path"
    )
    
    parser.add_argument(
        "--output", "-o",
        type=str,
        required=True,
        help="Output directory for processed files"
    )
    
    # Processing options
    parser.add_argument(
        "--no-obfuscation",
        action="store_true",
        help="Disable PDF obfuscation (only perform OCR and PII detection)"
    )
    
    parser.add_argument(
        "--pattern",
        type=str,
        default="*.pdf",
        help="File pattern for directory processing (default: *.pdf)"
    )
    
    parser.add_argument(
        "--no-recursive",
        action="store_true",
        help="Disable recursive directory scanning"
    )
    
    # Configuration overrides
    parser.add_argument(
        "--confidence-threshold",
        type=float,
        help=f"PII confidence threshold (default: {settings.confidence_threshold})"
    )
    
    parser.add_argument(
        "--obfuscation-method",
        type=str,
        choices=["black_box", "redact", "white_box", "blur", "hash_pattern"],
        help=f"Obfuscation method (default: {settings.obfuscation_method})"
    )
    
    parser.add_argument(
        "--max-concurrent",
        type=int,
        help=f"Maximum concurrent processing (default: {settings.max_concurrent_requests})"
    )
    
    # Enhanced features
    parser.add_argument(
        "--disable-ultra-think",
        action="store_true",
        help="Disable ULTRA THINK mode for faster but less accurate processing"
    )
    
    parser.add_argument(
        "--organization-id",
        type=str,
        help="Organization ID for database transaction recording"
    )
    
    parser.add_argument(
        "--user-id",
        type=str,
        help="User ID for audit logging"
    )
    
    parser.add_argument(
        "--session-id",
        type=str,
        help="Session ID for tracking"
    )
    
    # Utility options
    parser.add_argument(
        "--version",
        action="version",
        version="ChromoForge OCR Pipeline v2.0.0 - Enhanced with Gemini 2.5 Pro & ULTRA THINK"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Update log level if verbose
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Verbose logging enabled")
    
    # Validate input path
    input_path = Path(args.input)
    if not input_path.exists():
        logger.error(f"Input path does not exist: {input_path}")
        sys.exit(1)
    
    # Create output directory
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize enhanced application
    app = ChromoForgeApp(
        user_id=args.user_id,
        session_id=args.session_id
    )
    
    # Override configuration if specified
    if args.confidence_threshold:
        app.pii_detector.confidence_threshold = args.confidence_threshold
        logger.info(f"Confidence threshold set to: {args.confidence_threshold}")
    
    if args.obfuscation_method:
        app.pdf_obfuscator.method = ObfuscationMethod(args.obfuscation_method)
        logger.info(f"Obfuscation method set to: {args.obfuscation_method}")
    
    try:
        # Process based on input type
        if input_path.is_file():
            logger.info("Processing single file")
            result = await app.process_single_file(
                input_path,
                output_dir,
                enable_obfuscation=not args.no_obfuscation
            )
            
            if result["success"]:
                print(f"✓ Successfully processed: {input_path}")
                print(f"  Results saved to: {result['results_path']}")
                if result.get("obfuscation_result"):
                    print(f"  Obfuscated PDF: {result['obfuscation_result']['output_path']}")
            else:
                print(f"✗ Failed to process: {input_path}")
                print(f"  Error: {result.get('error', 'Unknown error')}")
                sys.exit(1)
        
        elif input_path.is_dir():
            logger.info("Processing directory")
            results = await app.process_directory(
                input_path,
                output_dir,
                pattern=args.pattern,
                recursive=not args.no_recursive,
                enable_obfuscation=not args.no_obfuscation
            )
            
            stats = results["batch_stats"]
            print(f"✓ Batch processing completed:")
            print(f"  Total files: {stats.total_files}")
            print(f"  Successful: {stats.completed_files}")
            print(f"  Failed: {stats.failed_files}")
            print(f"  Success rate: {stats.completed_files / stats.total_files * 100:.1f}%")
            print(f"  Average processing time: {stats.average_processing_time:.2f}s")
            print(f"  Total PII detected: {stats.total_pii_detected}")
            print(f"  Total PII obfuscated: {stats.total_pii_obfuscated}")
        
        else:
            logger.error(f"Input path is neither file nor directory: {input_path}")
            sys.exit(1)
    
    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        sys.exit(130)
    
    except Exception as e:
        logger.error(f"Application error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())