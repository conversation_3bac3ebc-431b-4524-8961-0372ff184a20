"""Core OCR processor using Google Gemini 2.5 Pro API."""

import asyncio
import base64
import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import google.generativeai as genai
import httpx
from google.generativeai.types import HarmCate<PERSON>y, HarmBlockThreshold
from pydantic import BaseModel, Field

from .config import get_gemini_config, get_processing_config


logger = logging.getLogger(__name__)


class OCRResult(BaseModel):
    """OCR processing result with extracted data and metadata."""
    
    # Extracted text content
    full_text: str = Field(description="Complete extracted text from document")
    
    # Structured medical data
    patient_name: Optional[str] = Field(None, description="Extracted patient name")
    thai_id: Optional[str] = Field(None, description="Thai national ID number")
    hospital_number: Optional[str] = Field(None, description="Hospital number (HN)")
    lab_number: Optional[str] = Field(None, description="Laboratory number (LN)")
    
    # Medical information
    diagnoses: List[str] = Field(default_factory=list, description="Medical diagnoses")
    medications: List[str] = Field(default_factory=list, description="Prescribed medications")
    test_results: Dict[str, Any] = Field(default_factory=dict, description="Lab test results")
    
    # Processing metadata
    confidence_score: float = Field(description="Overall confidence score (0-1)")
    processing_time: float = Field(description="Processing time in seconds")
    page_count: int = Field(description="Number of pages processed")
    
    # Language detection
    detected_languages: List[str] = Field(default_factory=list, description="Detected languages")
    
    # Error information
    errors: List[str] = Field(default_factory=list, description="Processing errors")
    warnings: List[str] = Field(default_factory=list, description="Processing warnings")


class GeminiOCRProcessor:
    """OCR processor using Google Gemini 2.5 Pro API."""
    
    def __init__(self):
        """Initialize the Gemini OCR processor."""
        self.config = get_gemini_config()
        self.processing_config = get_processing_config()
        
        # Configure Gemini API
        genai.configure(api_key=self.config["api_key"])
        
        # Initialize model with safety settings
        self.model = genai.GenerativeModel(
            model_name=self.config["model"],
            generation_config=genai.types.GenerationConfig(
                temperature=self.config["temperature"],
                max_output_tokens=self.config["max_tokens"],
            ),
            safety_settings={
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
        )
        
        logger.info(f"Initialized Gemini OCR processor with model: {self.config['model']}")
    
    async def process_pdf(self, pdf_path: Path) -> OCRResult:
        """Process a PDF file and extract structured medical data.
        
        Args:
            pdf_path: Path to the PDF file to process
            
        Returns:
            OCRResult with extracted data and metadata
        """
        import time
        start_time = time.time()
        
        logger.info(f"Starting OCR processing for: {pdf_path}")
        
        try:
            # Convert PDF to base64
            pdf_base64 = self._pdf_to_base64(pdf_path)
            
            # Generate OCR prompt
            prompt = self._create_ocr_prompt()
            
            # Process with Gemini API
            result = await self._process_with_gemini(pdf_base64, prompt)
            
            # Parse structured response
            ocr_result = self._parse_gemini_response(result)
            
            # Add processing metadata
            ocr_result.processing_time = time.time() - start_time
            ocr_result.page_count = self._get_page_count(pdf_path)
            
            logger.info(
                f"OCR processing completed in {ocr_result.processing_time:.2f}s "
                f"with confidence {ocr_result.confidence_score:.2f}"
            )
            
            return ocr_result
            
        except Exception as e:
            logger.error(f"OCR processing failed for {pdf_path}: {str(e)}")
            
            # Return error result
            return OCRResult(
                full_text="",
                confidence_score=0.0,
                processing_time=time.time() - start_time,
                page_count=0,
                errors=[str(e)]
            )
    
    async def process_pdf_with_retry(self, pdf_path: Path) -> OCRResult:
        """Process PDF with retry logic for failed requests.
        
        Args:
            pdf_path: Path to the PDF file to process
            
        Returns:
            OCRResult with extracted data and metadata
        """
        max_retries = self.processing_config["max_retries"]
        retry_delay = self.processing_config["retry_delay"]
        
        for attempt in range(max_retries + 1):
            try:
                result = await self.process_pdf(pdf_path)
                
                # If no errors, return result
                if not result.errors:
                    return result
                    
                # If this was the last attempt, return the error result
                if attempt == max_retries:
                    return result
                    
            except Exception as e:
                logger.warning(
                    f"OCR attempt {attempt + 1}/{max_retries + 1} failed for {pdf_path}: {str(e)}"
                )
                
                # If this was the last attempt, return error result
                if attempt == max_retries:
                    return OCRResult(
                        full_text="",
                        confidence_score=0.0,
                        processing_time=0.0,
                        page_count=0,
                        errors=[f"All {max_retries + 1} attempts failed: {str(e)}"]
                    )
            
            # Wait before retry
            if attempt < max_retries:
                await asyncio.sleep(retry_delay * (2 ** attempt))  # Exponential backoff
        
        # This should never be reached, but just in case
        return OCRResult(
            full_text="",
            confidence_score=0.0,
            processing_time=0.0,
            page_count=0,
            errors=["Maximum retries exceeded"]
        )
    
    def _pdf_to_base64(self, pdf_path: Path) -> str:
        """Convert PDF file to base64 string.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Base64 encoded PDF content
        """
        try:
            with open(pdf_path, "rb") as pdf_file:
                pdf_bytes = pdf_file.read()
                return base64.b64encode(pdf_bytes).decode("utf-8")
        except Exception as e:
            logger.error(f"Failed to convert PDF to base64: {str(e)}")
            raise
    
    def _create_ocr_prompt(self) -> str:
        """Create comprehensive OCR prompt for medical document processing.
        
        Returns:
            Formatted prompt string for Gemini API
        """
        return """
You are a medical OCR specialist. Please analyze this Thai medical document PDF and extract structured information. The document may contain:
- Mixed Thai and English text
- Handwritten notes and printed text
- Medical terminology in both languages
- Patient identification information
- Laboratory results and medical data

Please extract and return the following information in JSON format:

{
  "full_text": "Complete extracted text from the entire document",
  "patient_name": "Patient's full name (Thai or English)",
  "thai_id": "Thai national ID (13 digits, format: X-XXXX-XXXXX-XX-X)",
  "hospital_number": "Hospital number (HN: followed by numbers)",
  "lab_number": "Laboratory number (LN: or Lab No: followed by numbers/letters)",
  "diagnoses": ["List of medical diagnoses found"],
  "medications": ["List of prescribed medications"],
  "test_results": {
    "test_name": "result_value",
    "normal_ranges": "if provided"
  },
  "detected_languages": ["thai", "english"],
  "confidence_score": 0.95,
  "warnings": ["Any processing warnings or unclear text"]
}

IMPORTANT INSTRUCTIONS:
1. Extract ALL text, including handwritten content
2. Preserve original Thai script - do not transliterate
3. For patient identification, look for patterns like:
   - Thai ID: 13-digit numbers with dashes (X-XXXX-XXXXX-XX-X)
   - Hospital Number: "HN:", "HN ", "H.N:", followed by numbers
   - Lab Number: "LN:", "Lab No:", "LAB:", followed by alphanumeric
4. Be very careful with patient names - they may be in Thai or English script
5. Medical diagnoses may use ICD codes or descriptive text
6. Test results often have reference ranges in parentheses
7. Assign confidence score based on text clarity and completeness
8. Include warnings for unclear, damaged, or partially illegible text

Return only the JSON object, no additional text or formatting.
"""
    
    async def _process_with_gemini(self, pdf_base64: str, prompt: str) -> str:
        """Process PDF with Gemini API.
        
        Args:
            pdf_base64: Base64 encoded PDF content
            prompt: OCR prompt for Gemini
            
        Returns:
            Raw response from Gemini API
        """
        try:
            # Create the parts for multimodal input
            parts = [
                prompt,
                {
                    "inline_data": {
                        "mime_type": "application/pdf",
                        "data": pdf_base64
                    }
                }
            ]
            
            # Generate content with Gemini
            response = await asyncio.to_thread(
                self.model.generate_content,
                parts
            )
            
            # Check for blocked content
            if response.prompt_feedback.block_reason:
                raise ValueError(f"Content blocked: {response.prompt_feedback.block_reason}")
            
            # Extract text from response
            if response.candidates and response.candidates[0].content:
                return response.candidates[0].content.parts[0].text
            else:
                raise ValueError("No content generated by Gemini API")
                
        except Exception as e:
            logger.error(f"Gemini API processing failed: {str(e)}")
            raise
    
    def _parse_gemini_response(self, response_text: str) -> OCRResult:
        """Parse structured response from Gemini API.
        
        Args:
            response_text: Raw response text from Gemini
            
        Returns:
            Parsed OCRResult object
        """
        try:
            # Clean response text (remove markdown formatting if present)
            clean_text = response_text.strip()
            if clean_text.startswith("```json"):
                clean_text = clean_text[7:]
            if clean_text.endswith("```"):
                clean_text = clean_text[:-3]
            clean_text = clean_text.strip()
            
            # Parse JSON response
            parsed_data = json.loads(clean_text)
            
            # Create OCRResult with validation
            return OCRResult(
                full_text=parsed_data.get("full_text", ""),
                patient_name=parsed_data.get("patient_name"),
                thai_id=parsed_data.get("thai_id"),
                hospital_number=parsed_data.get("hospital_number"),
                lab_number=parsed_data.get("lab_number"),
                diagnoses=parsed_data.get("diagnoses", []),
                medications=parsed_data.get("medications", []),
                test_results=parsed_data.get("test_results", {}),
                detected_languages=parsed_data.get("detected_languages", []),
                confidence_score=float(parsed_data.get("confidence_score", 0.5)),
                warnings=parsed_data.get("warnings", []),
                processing_time=0.0,  # Will be set by caller
                page_count=0,  # Will be set by caller
                errors=[]
            )
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse Gemini response as JSON: {str(e)}")
            logger.debug(f"Raw response: {response_text}")
            
            # Return basic result with raw text
            return OCRResult(
                full_text=response_text,
                confidence_score=0.3,
                processing_time=0.0,
                page_count=0,
                errors=[f"JSON parsing failed: {str(e)}"]
            )
        
        except Exception as e:
            logger.error(f"Failed to parse Gemini response: {str(e)}")
            
            return OCRResult(
                full_text="",
                confidence_score=0.0,
                processing_time=0.0,
                page_count=0,
                errors=[f"Response parsing failed: {str(e)}"]
            )
    
    def _get_page_count(self, pdf_path: Path) -> int:
        """Get the number of pages in a PDF file.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Number of pages in the PDF
        """
        try:
            import PyPDF2
            
            with open(pdf_path, "rb") as pdf_file:
                pdf_reader = PyPDF2.PdfReader(pdf_file)
                return len(pdf_reader.pages)
                
        except Exception as e:
            logger.warning(f"Failed to get page count for {pdf_path}: {str(e)}")
            return 1  # Default to 1 page