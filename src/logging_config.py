"""Logging configuration for ChromoForge OCR pipeline."""

import logging
import logging.config
from pathlib import Path
import sys
from typing import Dict, Any
import structlog
from pythonjsonlogger import jsonlogger

from .config import settings


def setup_logging() -> None:
    """Set up comprehensive logging configuration."""
    
    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="ISO"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Logging configuration based on format preference
    if settings.log_format.lower() == "json":
        log_config = get_json_logging_config(log_dir)
    else:
        log_config = get_standard_logging_config(log_dir)
    
    # Apply configuration
    logging.config.dictConfig(log_config)
    
    # Set log levels for external libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("google").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured with format: {settings.log_format}, level: {settings.log_level}")


def get_json_logging_config(log_dir: Path) -> Dict[str, Any]:
    """Get JSON-formatted logging configuration."""
    
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": jsonlogger.JsonFormatter,
                "format": "%(asctime)s %(name)s %(levelname)s %(message)s %(pathname)s %(lineno)d"
            },
            "simple": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.log_level,
                "formatter": "simple",
                "stream": sys.stdout
            },
            "file_json": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "DEBUG",
                "formatter": "json",
                "filename": str(log_dir / "chromoforge.jsonl"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8"
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "json",
                "filename": str(log_dir / "errors.jsonl"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10,
                "encoding": "utf-8"
            },
            "ocr_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "DEBUG",
                "formatter": "json",
                "filename": str(log_dir / "ocr_processing.jsonl"),
                "maxBytes": 20971520,  # 20MB
                "backupCount": 3,
                "encoding": "utf-8"
            }
        },
        "loggers": {
            "src.ocr_processor": {
                "level": "DEBUG",
                "handlers": ["console", "file_json", "ocr_file"],
                "propagate": False
            },
            "src.pii_detector": {
                "level": "DEBUG",
                "handlers": ["console", "file_json"],
                "propagate": False
            },
            "src.pdf_obfuscator": {
                "level": "DEBUG",
                "handlers": ["console", "file_json"],
                "propagate": False
            },
            "src.batch_processor": {
                "level": "DEBUG",
                "handlers": ["console", "file_json"],
                "propagate": False
            }
        },
        "root": {
            "level": settings.log_level,
            "handlers": ["console", "file_json", "error_file"]
        }
    }


def get_standard_logging_config(log_dir: Path) -> Dict[str, Any]:
    """Get standard text-formatted logging configuration."""
    
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "simple": {
                "format": "%(asctime)s - %(levelname)s - %(message)s",
                "datefmt": "%H:%M:%S"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.log_level,
                "formatter": "simple",
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "DEBUG",
                "formatter": "detailed",
                "filename": str(log_dir / "chromoforge.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8"
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": str(log_dir / "errors.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10,
                "encoding": "utf-8"
            }
        },
        "root": {
            "level": settings.log_level,
            "handlers": ["console", "file", "error_file"]
        }
    }


class OCRLogger:
    """Specialized logger for OCR operations with structured logging."""
    
    def __init__(self, name: str):
        """Initialize OCR logger."""
        self.logger = structlog.get_logger(name)
    
    def log_ocr_start(self, file_path: str, file_size: int):
        """Log OCR processing start."""
        self.logger.info(
            "OCR processing started",
            file_path=file_path,
            file_size_mb=round(file_size / 1024 / 1024, 2),
            event_type="ocr_start"
        )
    
    def log_ocr_complete(self, file_path: str, processing_time: float, confidence: float, pii_count: int):
        """Log OCR processing completion."""
        self.logger.info(
            "OCR processing completed",
            file_path=file_path,
            processing_time=processing_time,
            confidence_score=confidence,
            pii_items_detected=pii_count,
            event_type="ocr_complete"
        )
    
    def log_ocr_error(self, file_path: str, error_message: str, retry_count: int = 0):
        """Log OCR processing error."""
        self.logger.error(
            "OCR processing failed",
            file_path=file_path,
            error_message=error_message,
            retry_count=retry_count,
            event_type="ocr_error"
        )
    
    def log_pii_detection(self, file_path: str, pii_types: Dict[str, int], total_matches: int):
        """Log PII detection results."""
        self.logger.info(
            "PII detection completed",
            file_path=file_path,
            pii_types=pii_types,
            total_pii_matches=total_matches,
            event_type="pii_detection"
        )
    
    def log_obfuscation_complete(self, file_path: str, output_path: str, pii_obfuscated: int, method: str):
        """Log PDF obfuscation completion."""
        self.logger.info(
            "PDF obfuscation completed",
            input_file=file_path,
            output_file=output_path,
            pii_items_obfuscated=pii_obfuscated,
            obfuscation_method=method,
            event_type="obfuscation_complete"
        )
    
    def log_batch_stats(self, total_files: int, completed: int, failed: int, avg_time: float):
        """Log batch processing statistics."""
        self.logger.info(
            "Batch processing statistics",
            total_files=total_files,
            completed_files=completed,
            failed_files=failed,
            success_rate=round(completed / total_files * 100, 1) if total_files > 0 else 0,
            average_processing_time=avg_time,
            event_type="batch_stats"
        )