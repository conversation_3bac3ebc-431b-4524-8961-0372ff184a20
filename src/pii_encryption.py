"""PII encryption module for secure data storage and processing.

This module provides comprehensive encryption capabilities for personally identifiable
information (PII) to ensure compliance with data protection regulations and secure
storage of sensitive data.
"""

import base64
import hashlib
import secrets
from datetime import datetime, timezone
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
import json
import threading
from dataclasses import dataclass, asdict

from cryptography.fernet import <PERSON><PERSON><PERSON>, MultiFernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend

from .exceptions import EncryptionException, handle_exception
from .audit_logger import AuditEventType, get_audit_logger


class EncryptionType(Enum):
    """Types of encryption methods available."""
    SYMMETRIC_FERNET = "symmetric_fernet"
    HYBRID_RSA_AES = "hybrid_rsa_aes"
    DETERMINISTIC_HASH = "deterministic_hash"


class PIIClassification(Enum):
    """Classification levels for PII data."""
    PUBLIC = "public"           # No encryption needed
    INTERNAL = "internal"       # Basic encryption
    CONFIDENTIAL = "confidential"  # Strong encryption
    RESTRICTED = "restricted"   # Maximum encryption + audit


@dataclass
class EncryptedData:
    """Container for encrypted data with metadata.
    
    Attributes:
        encrypted_value: Base64-encoded encrypted data
        encryption_type: Type of encryption used
        key_id: Identifier of the encryption key used
        salt: Salt used for key derivation (if applicable)
        iv: Initialization vector (if applicable)
        timestamp: When the data was encrypted
        classification: PII classification level
        metadata: Additional metadata about the encryption
    """
    encrypted_value: str
    encryption_type: str
    key_id: str
    salt: Optional[str] = None
    iv: Optional[str] = None
    timestamp: str = None
    classification: str = PIIClassification.CONFIDENTIAL.value
    metadata: Dict[str, Any] = None
    
    def __post_init__(self) -> None:
        """Initialize default values after creation."""
        if self.timestamp is None:
            self.timestamp = datetime.now(timezone.utc).isoformat()
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), ensure_ascii=False)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EncryptedData':
        """Create from dictionary."""
        return cls(**data)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'EncryptedData':
        """Create from JSON string."""
        return cls.from_dict(json.loads(json_str))


class KeyManager:
    """Manages encryption keys with rotation and secure storage."""
    
    def __init__(
        self,
        key_storage_path: Optional[Path] = None,
        master_key: Optional[bytes] = None
    ) -> None:
        """Initialize key manager.
        
        Args:
            key_storage_path: Path to store encryption keys
            master_key: Master key for key encryption (generated if None)
        """
        self.key_storage_path = key_storage_path
        self._lock = threading.RLock()
        
        # Master key for encrypting other keys
        if master_key:
            self.master_key = master_key
        else:
            self.master_key = self._generate_master_key()
        
        # Key storage
        self._keys: Dict[str, bytes] = {}
        self._key_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Load existing keys if storage path provided
        if self.key_storage_path:
            self._load_keys()
    
    def _generate_master_key(self) -> bytes:
        """Generate a new master key.
        
        Returns:
            32-byte master key
        """
        return secrets.token_bytes(32)
    
    def generate_key(
        self,
        key_id: str,
        key_type: str = "fernet",
        key_size: int = 32
    ) -> str:
        """Generate a new encryption key.
        
        Args:
            key_id: Unique identifier for the key
            key_type: Type of key to generate
            key_size: Size of the key in bytes
            
        Returns:
            Key ID for the generated key
            
        Raises:
            EncryptionException: If key generation fails
        """
        try:
            with self._lock:
                if key_id in self._keys:
                    raise EncryptionException(
                        message=f"Key with ID '{key_id}' already exists",
                        operation="generate_key",
                        context={"key_id": key_id}
                    )
                
                # Generate key based on type
                if key_type == "fernet":
                    key = Fernet.generate_key()
                elif key_type == "aes":
                    key = secrets.token_bytes(key_size)
                else:
                    raise EncryptionException(
                        message=f"Unsupported key type: {key_type}",
                        operation="generate_key",
                        context={"key_type": key_type}
                    )
                
                # Store key and metadata
                self._keys[key_id] = key
                self._key_metadata[key_id] = {
                    "key_type": key_type,
                    "key_size": len(key),
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "usage_count": 0,
                    "active": True
                }
                
                # Save to storage if configured
                if self.key_storage_path:
                    self._save_keys()
                
                # Log key generation
                audit_logger = get_audit_logger()
                audit_logger.log_event(
                    event_type=AuditEventType.SYSTEM_ERROR,  # No specific key generation event
                    action="generate_encryption_key",
                    resource="encryption_key",
                    resource_id=key_id,
                    metadata={"key_type": key_type, "key_size": len(key)},
                    compliance_tags=["key_management", "encryption"]
                )
                
                return key_id
                
        except Exception as e:
            raise EncryptionException(
                message=f"Failed to generate key: {str(e)}",
                operation="generate_key",
                context={"key_id": key_id, "key_type": key_type},
                original_exception=e
            )
    
    def get_key(self, key_id: str) -> bytes:
        """Get encryption key by ID.
        
        Args:
            key_id: Key identifier
            
        Returns:
            Encryption key bytes
            
        Raises:
            EncryptionException: If key not found or inactive
        """
        with self._lock:
            if key_id not in self._keys:
                raise EncryptionException(
                    message=f"Key not found: {key_id}",
                    operation="get_key",
                    context={"key_id": key_id}
                )
            
            metadata = self._key_metadata.get(key_id, {})
            if not metadata.get("active", True):
                raise EncryptionException(
                    message=f"Key is inactive: {key_id}",
                    operation="get_key",
                    context={"key_id": key_id}
                )
            
            # Update usage count
            metadata["usage_count"] = metadata.get("usage_count", 0) + 1
            metadata["last_used"] = datetime.now(timezone.utc).isoformat()
            
            return self._keys[key_id]
    
    def rotate_key(self, old_key_id: str, new_key_id: str) -> str:
        """Rotate encryption key.
        
        Args:
            old_key_id: ID of the key to rotate
            new_key_id: ID for the new key
            
        Returns:
            New key ID
            
        Raises:
            EncryptionException: If rotation fails
        """
        try:
            with self._lock:
                # Get old key metadata
                old_metadata = self._key_metadata.get(old_key_id, {})
                key_type = old_metadata.get("key_type", "fernet")
                
                # Generate new key
                self.generate_key(new_key_id, key_type)
                
                # Mark old key as inactive
                if old_key_id in self._key_metadata:
                    self._key_metadata[old_key_id]["active"] = False
                    self._key_metadata[old_key_id]["rotated_at"] = datetime.now(timezone.utc).isoformat()
                    self._key_metadata[old_key_id]["rotated_to"] = new_key_id
                
                # Log key rotation
                audit_logger = get_audit_logger()
                audit_logger.log_event(
                    event_type=AuditEventType.CONFIGURATION_CHANGED,
                    action="rotate_encryption_key",
                    resource="encryption_key",
                    resource_id=old_key_id,
                    metadata={
                        "new_key_id": new_key_id,
                        "old_key_usage_count": old_metadata.get("usage_count", 0)
                    },
                    compliance_tags=["key_rotation", "encryption"]
                )
                
                return new_key_id
                
        except Exception as e:
            raise EncryptionException(
                message=f"Failed to rotate key: {str(e)}",
                operation="rotate_key",
                context={"old_key_id": old_key_id, "new_key_id": new_key_id},
                original_exception=e
            )
    
    def _save_keys(self) -> None:
        """Save keys to secure storage."""
        if not self.key_storage_path:
            return
        
        try:
            # Encrypt keys with master key
            f = Fernet(base64.urlsafe_b64encode(self.master_key))
            
            key_data = {
                "keys": {
                    key_id: base64.b64encode(key).decode()
                    for key_id, key in self._keys.items()
                },
                "metadata": self._key_metadata
            }
            
            encrypted_data = f.encrypt(json.dumps(key_data).encode())
            
            # Write to file
            self.key_storage_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.key_storage_path, 'wb') as f_out:
                f_out.write(encrypted_data)
                
        except Exception as e:
            raise EncryptionException(
                message=f"Failed to save keys: {str(e)}",
                operation="save_keys",
                original_exception=e
            )
    
    def _load_keys(self) -> None:
        """Load keys from secure storage."""
        if not self.key_storage_path or not self.key_storage_path.exists():
            return
        
        try:
            # Read and decrypt key data
            with open(self.key_storage_path, 'rb') as f_in:
                encrypted_data = f_in.read()
            
            f = Fernet(base64.urlsafe_b64encode(self.master_key))
            decrypted_data = f.decrypt(encrypted_data)
            key_data = json.loads(decrypted_data.decode())
            
            # Load keys and metadata
            self._keys = {
                key_id: base64.b64decode(key_b64)
                for key_id, key_b64 in key_data.get("keys", {}).items()
            }
            self._key_metadata = key_data.get("metadata", {})
            
        except Exception as e:
            raise EncryptionException(
                message=f"Failed to load keys: {str(e)}",
                operation="load_keys",
                original_exception=e
            )


class PIIEncryption:
    """PII encryption service with multiple encryption methods and key management."""
    
    def __init__(
        self,
        key_manager: Optional[KeyManager] = None,
        default_key_id: str = "default_pii_key"
    ) -> None:
        """Initialize PII encryption service.
        
        Args:
            key_manager: Key manager instance
            default_key_id: Default key ID for encryption
        """
        self.key_manager = key_manager or KeyManager()
        self.default_key_id = default_key_id
        self._lock = threading.RLock()
        
        # Ensure default key exists
        self._ensure_default_key()
    
    def _ensure_default_key(self) -> None:
        """Ensure default encryption key exists."""
        try:
            self.key_manager.get_key(self.default_key_id)
        except EncryptionException:
            # Generate default key if it doesn't exist
            self.key_manager.generate_key(self.default_key_id, "fernet")
    
    def encrypt_pii(
        self,
        pii_value: str,
        pii_type: str,
        classification: PIIClassification = PIIClassification.CONFIDENTIAL,
        key_id: Optional[str] = None,
        encryption_type: EncryptionType = EncryptionType.SYMMETRIC_FERNET,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> EncryptedData:
        """Encrypt PII data.
        
        Args:
            pii_value: PII value to encrypt
            pii_type: Type of PII (e.g., 'thai_id', 'name')
            classification: PII classification level
            key_id: Encryption key ID (uses default if None)
            encryption_type: Type of encryption to use
            user_id: User ID for audit logging
            session_id: Session ID for audit logging
            
        Returns:
            EncryptedData object containing encrypted value and metadata
            
        Raises:
            EncryptionException: If encryption fails
        """
        try:
            with self._lock:
                # Use default key if none specified
                actual_key_id = key_id or self.default_key_id
                
                # Get encryption key
                encryption_key = self.key_manager.get_key(actual_key_id)
                
                # Perform encryption based on type
                if encryption_type == EncryptionType.SYMMETRIC_FERNET:
                    encrypted_value = self._encrypt_fernet(pii_value, encryption_key)
                    salt = None
                    iv = None
                elif encryption_type == EncryptionType.DETERMINISTIC_HASH:
                    # For searchable encryption (one-way hash)
                    encrypted_value, salt = self._encrypt_deterministic_hash(pii_value)
                    iv = None
                else:
                    raise EncryptionException(
                        message=f"Unsupported encryption type: {encryption_type}",
                        operation="encrypt_pii",
                        context={"encryption_type": encryption_type.value}
                    )
                
                # Create encrypted data object
                encrypted_data = EncryptedData(
                    encrypted_value=encrypted_value,
                    encryption_type=encryption_type.value,
                    key_id=actual_key_id,
                    salt=salt,
                    iv=iv,
                    classification=classification.value,
                    metadata={
                        "pii_type": pii_type,
                        "original_length": len(pii_value)
                    }
                )
                
                # Log encryption event
                audit_logger = get_audit_logger()
                audit_logger.log_event(
                    event_type=AuditEventType.PII_ENCRYPTED,
                    action="encrypt_pii",
                    resource="pii_data",
                    user_id=user_id,
                    session_id=session_id,
                    metadata={
                        "pii_type": pii_type,
                        "classification": classification.value,
                        "encryption_type": encryption_type.value,
                        "key_id": actual_key_id
                    },
                    pii_types=[pii_type],
                    compliance_tags=["pii_encryption", "data_protection"]
                )
                
                return encrypted_data
                
        except Exception as e:
            # Log encryption failure
            audit_logger = get_audit_logger()
            audit_logger.log_event(
                event_type=AuditEventType.PII_ENCRYPTED,
                action="encrypt_pii",
                resource="pii_data",
                success=False,
                error_code=getattr(e, 'error_code', 'ENCRYPT_FAIL'),
                user_id=user_id,
                session_id=session_id,
                metadata={
                    "pii_type": pii_type,
                    "classification": classification.value,
                    "encryption_type": encryption_type.value,
                    "error": str(e)
                },
                pii_types=[pii_type],
                compliance_tags=["pii_encryption", "encryption_failure"]
            )
            
            if isinstance(e, EncryptionException):
                raise
            else:
                raise EncryptionException(
                    message=f"Failed to encrypt PII: {str(e)}",
                    operation="encrypt_pii",
                    context={
                        "pii_type": pii_type,
                        "classification": classification.value,
                        "encryption_type": encryption_type.value
                    },
                    original_exception=e
                )
    
    def decrypt_pii(
        self,
        encrypted_data: EncryptedData,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        access_reason: Optional[str] = None
    ) -> str:
        """Decrypt PII data.
        
        Args:
            encrypted_data: EncryptedData object to decrypt
            user_id: User ID for audit logging
            session_id: Session ID for audit logging
            access_reason: Reason for accessing PII
            
        Returns:
            Decrypted PII value
            
        Raises:
            EncryptionException: If decryption fails
        """
        try:
            with self._lock:
                # Get decryption key
                decryption_key = self.key_manager.get_key(encrypted_data.key_id)
                
                # Perform decryption based on type
                encryption_type = EncryptionType(encrypted_data.encryption_type)
                
                if encryption_type == EncryptionType.SYMMETRIC_FERNET:
                    decrypted_value = self._decrypt_fernet(
                        encrypted_data.encrypted_value,
                        decryption_key
                    )
                elif encryption_type == EncryptionType.DETERMINISTIC_HASH:
                    raise EncryptionException(
                        message="Cannot decrypt deterministic hash - it's one-way only",
                        operation="decrypt_pii",
                        context={"encryption_type": encryption_type.value}
                    )
                else:
                    raise EncryptionException(
                        message=f"Unsupported encryption type: {encryption_type}",
                        operation="decrypt_pii",
                        context={"encryption_type": encryption_type.value}
                    )
                
                # Log decryption event
                audit_logger = get_audit_logger()
                audit_logger.log_event(
                    event_type=AuditEventType.PII_DECRYPTED,
                    action="decrypt_pii",
                    resource="pii_data",
                    user_id=user_id,
                    session_id=session_id,
                    metadata={
                        "pii_type": encrypted_data.metadata.get("pii_type", "unknown"),
                        "classification": encrypted_data.classification,
                        "encryption_type": encrypted_data.encryption_type,
                        "key_id": encrypted_data.key_id,
                        "access_reason": access_reason
                    },
                    pii_types=[encrypted_data.metadata.get("pii_type", "unknown")],
                    compliance_tags=["pii_decryption", "pii_access"]
                )
                
                return decrypted_value
                
        except Exception as e:
            # Log decryption failure
            audit_logger = get_audit_logger()
            audit_logger.log_event(
                event_type=AuditEventType.PII_DECRYPTED,
                action="decrypt_pii",
                resource="pii_data",
                success=False,
                error_code=getattr(e, 'error_code', 'DECRYPT_FAIL'),
                user_id=user_id,
                session_id=session_id,
                metadata={
                    "pii_type": encrypted_data.metadata.get("pii_type", "unknown"),
                    "classification": encrypted_data.classification,
                    "encryption_type": encrypted_data.encryption_type,
                    "key_id": encrypted_data.key_id,
                    "error": str(e)
                },
                pii_types=[encrypted_data.metadata.get("pii_type", "unknown")],
                compliance_tags=["pii_decryption", "decryption_failure"]
            )
            
            if isinstance(e, EncryptionException):
                raise
            else:
                raise EncryptionException(
                    message=f"Failed to decrypt PII: {str(e)}",
                    operation="decrypt_pii",
                    context={
                        "encryption_type": encrypted_data.encryption_type,
                        "key_id": encrypted_data.key_id
                    },
                    original_exception=e
                )
    
    def _encrypt_fernet(self, value: str, key: bytes) -> str:
        """Encrypt value using Fernet symmetric encryption.
        
        Args:
            value: Value to encrypt
            key: Encryption key
            
        Returns:
            Base64-encoded encrypted value
        """
        f = Fernet(key)
        encrypted_bytes = f.encrypt(value.encode('utf-8'))
        return base64.b64encode(encrypted_bytes).decode('ascii')
    
    def _decrypt_fernet(self, encrypted_value: str, key: bytes) -> str:
        """Decrypt value using Fernet symmetric encryption.
        
        Args:
            encrypted_value: Base64-encoded encrypted value
            key: Decryption key
            
        Returns:
            Decrypted value
        """
        f = Fernet(key)
        encrypted_bytes = base64.b64decode(encrypted_value.encode('ascii'))
        decrypted_bytes = f.decrypt(encrypted_bytes)
        return decrypted_bytes.decode('utf-8')
    
    def _encrypt_deterministic_hash(self, value: str) -> Tuple[str, str]:
        """Create deterministic hash for searchable encryption.
        
        Args:
            value: Value to hash
            
        Returns:
            Tuple of (hash_value, salt)
        """
        # Generate salt
        salt = secrets.token_hex(16)
        
        # Create hash with salt
        hash_obj = hashlib.pbkdf2_hmac(
            'sha256',
            value.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # iterations
        )
        
        hash_value = base64.b64encode(hash_obj).decode('ascii')
        return hash_value, salt
    
    def verify_hash(self, value: str, encrypted_data: EncryptedData) -> bool:
        """Verify if a value matches a deterministic hash.
        
        Args:
            value: Value to verify
            encrypted_data: Encrypted data with hash to verify against
            
        Returns:
            True if value matches the hash
        """
        if encrypted_data.encryption_type != EncryptionType.DETERMINISTIC_HASH.value:
            raise EncryptionException(
                message="Can only verify deterministic hashes",
                operation="verify_hash",
                context={"encryption_type": encrypted_data.encryption_type}
            )
        
        if not encrypted_data.salt:
            raise EncryptionException(
                message="Salt required for hash verification",
                operation="verify_hash"
            )
        
        # Recreate hash with same salt
        hash_obj = hashlib.pbkdf2_hmac(
            'sha256',
            value.encode('utf-8'),
            encrypted_data.salt.encode('utf-8'),
            100000
        )
        
        computed_hash = base64.b64encode(hash_obj).decode('ascii')
        return computed_hash == encrypted_data.encrypted_value


# Global encryption service instance
_pii_encryption: Optional[PIIEncryption] = None


def initialize_pii_encryption(
    key_storage_path: Optional[Path] = None,
    master_key: Optional[bytes] = None
) -> PIIEncryption:
    """Initialize global PII encryption service.
    
    Args:
        key_storage_path: Path to store encryption keys
        master_key: Master key for key encryption
        
    Returns:
        Initialized PII encryption service
    """
    global _pii_encryption
    
    key_manager = KeyManager(
        key_storage_path=key_storage_path,
        master_key=master_key
    )
    
    _pii_encryption = PIIEncryption(key_manager=key_manager)
    return _pii_encryption


def get_pii_encryption() -> PIIEncryption:
    """Get global PII encryption service.
    
    Returns:
        Global PII encryption service instance
        
    Raises:
        RuntimeError: If PII encryption not initialized
    """
    global _pii_encryption
    
    if _pii_encryption is None:
        # Initialize with default settings
        _pii_encryption = PIIEncryption()
    
    return _pii_encryption