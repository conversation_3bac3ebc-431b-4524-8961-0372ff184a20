"""PDF obfuscation module with coordinate-based PII masking."""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import tempfile
import fitz  # PyMuPDF
from dataclasses import dataclass
from enum import Enum

from reportlab.pdfgen import canvas
from reportlab.lib.colors import black
from reportlab.lib.pagesizes import letter

from .pii_detector import PIIMatch, PIIType


logger = logging.getLogger(__name__)


class ObfuscationMethod(Enum):
    """Methods for obfuscating PII in PDFs."""
    BLACK_BOX = "black_box"        # Black rectangle overlay
    BLUR = "blur"                  # Gaussian blur effect
    REDACT = "redact"             # Complete text removal
    HASH_PATTERN = "hash_pattern"  # Hash/strikethrough pattern
    WHITE_BOX = "white_box"        # White rectangle overlay


@dataclass
class TextCoordinate:
    """Represents text location in PDF coordinates."""
    x1: float
    y1: float
    x2: float
    y2: float
    page_num: int
    text: str
    font_size: float = 12.0
    
    @property
    def width(self) -> float:
        """Get width of the text box."""
        return self.x2 - self.x1
    
    @property
    def height(self) -> float:
        """Get height of the text box."""
        return self.y2 - self.y1
    
    @property
    def center(self) -> Tuple[float, float]:
        """Get center coordinates of the text box."""
        return ((self.x1 + self.x2) / 2, (self.y1 + self.y2) / 2)


class PDFTextExtractor:
    """Extract text with coordinate information from PDFs."""
    
    def __init__(self):
        """Initialize PDF text extractor."""
        self.min_font_size = 6.0  # Minimum font size to consider
        self.max_font_size = 72.0  # Maximum font size to consider
        
    def extract_text_with_coordinates(self, pdf_path: Path) -> Tuple[str, List[TextCoordinate]]:
        """Extract text and coordinate information from PDF.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Tuple of (full_text, list of TextCoordinate objects)
        """
        try:
            doc = fitz.open(str(pdf_path))
            full_text = ""
            text_coordinates = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # Extract text with detailed information
                text_dict = page.get_text("dict")
                page_text = ""
                
                for block in text_dict["blocks"]:
                    if "lines" in block:  # Text block
                        for line in block["lines"]:
                            line_text = ""
                            line_coords = []
                            
                            for span in line["spans"]:
                                span_text = span["text"]
                                if span_text.strip():
                                    # Get bounding box
                                    bbox = span["bbox"]
                                    font_size = span["size"]
                                    
                                    # Filter by font size
                                    if self.min_font_size <= font_size <= self.max_font_size:
                                        coord = TextCoordinate(
                                            x1=bbox[0],
                                            y1=bbox[1],
                                            x2=bbox[2],
                                            y2=bbox[3],
                                            page_num=page_num,
                                            text=span_text,
                                            font_size=font_size
                                        )
                                        text_coordinates.append(coord)
                                        line_text += span_text
                                        line_coords.append(coord)
                            
                            if line_text.strip():
                                page_text += line_text + "\n"
                
                full_text += page_text
            
            doc.close()
            
            logger.info(f"Extracted {len(text_coordinates)} text spans from {len(doc)} pages")
            return full_text, text_coordinates
            
        except Exception as e:
            logger.error(f"Failed to extract text coordinates from {pdf_path}: {str(e)}")
            raise


class PIICoordinateMapper:
    """Map PII matches to PDF coordinates."""
    
    def __init__(self, tolerance: float = 0.8):
        """Initialize coordinate mapper.
        
        Args:
            tolerance: Text matching tolerance (0.0-1.0)
        """
        self.tolerance = tolerance
        
    def map_pii_to_coordinates(
        self, 
        pii_matches: List[PIIMatch], 
        text_coordinates: List[TextCoordinate],
        full_text: str
    ) -> List[Tuple[PIIMatch, List[TextCoordinate]]]:
        """Map PII matches to their corresponding PDF coordinates.
        
        Args:
            pii_matches: List of detected PII matches
            text_coordinates: List of text coordinates from PDF
            full_text: Full extracted text
            
        Returns:
            List of tuples (PIIMatch, corresponding TextCoordinates)
        """
        mapped_results = []
        
        for pii_match in pii_matches:
            # Find text coordinates that correspond to this PII match
            matching_coords = self._find_matching_coordinates(
                pii_match, text_coordinates, full_text
            )
            
            if matching_coords:
                mapped_results.append((pii_match, matching_coords))
                logger.debug(
                    f"Mapped PII '{pii_match.text}' to {len(matching_coords)} coordinates"
                )
            else:
                logger.warning(f"No coordinates found for PII: {pii_match.text}")
        
        return mapped_results
    
    def _find_matching_coordinates(
        self, 
        pii_match: PIIMatch, 
        text_coordinates: List[TextCoordinate],
        full_text: str
    ) -> List[TextCoordinate]:
        """Find coordinates that match a PII string.
        
        Args:
            pii_match: PII match to find coordinates for
            text_coordinates: Available text coordinates
            full_text: Full extracted text
            
        Returns:
            List of matching TextCoordinate objects
        """
        pii_text = pii_match.text.strip()
        matching_coords = []
        
        # Try exact match first
        exact_matches = self._find_exact_matches(pii_text, text_coordinates)
        if exact_matches:
            return exact_matches
        
        # Try fuzzy matching for names and complex text
        if pii_match.pii_type in [PIIType.THAI_NAME, PIIType.ENGLISH_NAME, PIIType.ADDRESS]:
            fuzzy_matches = self._find_fuzzy_matches(pii_text, text_coordinates)
            if fuzzy_matches:
                return fuzzy_matches
        
        # Try word-by-word matching for multi-word PII
        words = pii_text.split()
        if len(words) > 1:
            word_matches = self._find_word_matches(words, text_coordinates)
            if word_matches:
                return word_matches
        
        return matching_coords
    
    def _find_exact_matches(
        self, 
        pii_text: str, 
        text_coordinates: List[TextCoordinate]
    ) -> List[TextCoordinate]:
        """Find exact text matches in coordinates."""
        matches = []
        
        for coord in text_coordinates:
            if pii_text in coord.text or coord.text in pii_text:
                matches.append(coord)
        
        return matches
    
    def _find_fuzzy_matches(
        self, 
        pii_text: str, 
        text_coordinates: List[TextCoordinate]
    ) -> List[TextCoordinate]:
        """Find fuzzy text matches using string similarity."""
        try:
            from Levenshtein import ratio
            
            matches = []
            threshold = self.tolerance
            
            for coord in text_coordinates:
                similarity = ratio(pii_text.lower(), coord.text.lower())
                if similarity >= threshold:
                    matches.append(coord)
            
            return matches
            
        except ImportError:
            logger.warning("Levenshtein not available, falling back to simple matching")
            return self._find_simple_fuzzy_matches(pii_text, text_coordinates)
    
    def _find_simple_fuzzy_matches(
        self, 
        pii_text: str, 
        text_coordinates: List[TextCoordinate]
    ) -> List[TextCoordinate]:
        """Simple fuzzy matching without external dependencies."""
        matches = []
        pii_words = set(pii_text.lower().split())
        
        for coord in text_coordinates:
            coord_words = set(coord.text.lower().split())
            
            # Check word overlap
            overlap = len(pii_words.intersection(coord_words))
            if overlap > 0 and overlap / len(pii_words) >= self.tolerance:
                matches.append(coord)
        
        return matches
    
    def _find_word_matches(
        self, 
        words: List[str], 
        text_coordinates: List[TextCoordinate]
    ) -> List[TextCoordinate]:
        """Find coordinates for individual words in multi-word PII."""
        matches = []
        
        for word in words:
            word_matches = self._find_exact_matches(word, text_coordinates)
            matches.extend(word_matches)
        
        return matches


class PDFObfuscator:
    """Obfuscate PII in PDF files using coordinate-based masking."""
    
    def __init__(self, method: ObfuscationMethod = ObfuscationMethod.BLACK_BOX):
        """Initialize PDF obfuscator.
        
        Args:
            method: Obfuscation method to use
        """
        self.method = method
        self.text_extractor = PDFTextExtractor()
        self.coordinate_mapper = PIICoordinateMapper()
        
        # Padding around text boxes (in points)
        self.padding = 2.0
        
        logger.info(f"PDF obfuscator initialized with method: {method.value}")
    
    async def obfuscate_pdf(
        self, 
        input_path: Path, 
        output_path: Path, 
        pii_matches: List[PIIMatch]
    ) -> Dict[str, any]:
        """Obfuscate PII in a PDF file.
        
        Args:
            input_path: Path to input PDF
            output_path: Path to save obfuscated PDF
            pii_matches: List of PII matches to obfuscate
            
        Returns:
            Dictionary with obfuscation results and metadata
        """
        try:
            logger.info(f"Starting PDF obfuscation: {input_path} -> {output_path}")
            
            # Extract text with coordinates
            full_text, text_coordinates = self.text_extractor.extract_text_with_coordinates(input_path)
            
            # Map PII to coordinates
            pii_coordinate_mapping = self.coordinate_mapper.map_pii_to_coordinates(
                pii_matches, text_coordinates, full_text
            )
            
            # Perform obfuscation
            obfuscation_result = await self._apply_obfuscation(
                input_path, output_path, pii_coordinate_mapping
            )
            
            # Prepare result metadata
            result = {
                "success": True,
                "input_path": str(input_path),
                "output_path": str(output_path),
                "method": self.method.value,
                "pii_items_found": len(pii_matches),
                "pii_items_obfuscated": len([m for m, c in pii_coordinate_mapping if c]),
                "obfuscated_coordinates": [
                    {
                        "pii_type": match.pii_type.value,
                        "text": match.text,
                        "coordinates": [
                            {
                                "page": coord.page_num,
                                "bbox": [coord.x1, coord.y1, coord.x2, coord.y2]
                            }
                            for coord in coords
                        ]
                    }
                    for match, coords in pii_coordinate_mapping if coords
                ],
                "processing_stats": obfuscation_result
            }
            
            logger.info(
                f"PDF obfuscation completed: {result['pii_items_obfuscated']}/{result['pii_items_found']} items obfuscated"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"PDF obfuscation failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "input_path": str(input_path),
                "output_path": str(output_path)
            }
    
    async def _apply_obfuscation(
        self, 
        input_path: Path, 
        output_path: Path, 
        pii_coordinate_mapping: List[Tuple[PIIMatch, List[TextCoordinate]]]
    ) -> Dict[str, any]:
        """Apply obfuscation method to PDF.
        
        Args:
            input_path: Input PDF path
            output_path: Output PDF path
            pii_coordinate_mapping: PII to coordinate mappings
            
        Returns:
            Processing statistics
        """
        if self.method == ObfuscationMethod.BLACK_BOX:
            return await self._apply_black_box_obfuscation(
                input_path, output_path, pii_coordinate_mapping
            )
        elif self.method == ObfuscationMethod.REDACT:
            return await self._apply_redaction_obfuscation(
                input_path, output_path, pii_coordinate_mapping
            )
        elif self.method == ObfuscationMethod.WHITE_BOX:
            return await self._apply_white_box_obfuscation(
                input_path, output_path, pii_coordinate_mapping
            )
        else:
            raise NotImplementedError(f"Obfuscation method {self.method} not implemented")
    
    async def _apply_black_box_obfuscation(
        self, 
        input_path: Path, 
        output_path: Path, 
        pii_coordinate_mapping: List[Tuple[PIIMatch, List[TextCoordinate]]]
    ) -> Dict[str, any]:
        """Apply black box obfuscation using PyMuPDF."""
        doc = fitz.open(str(input_path))
        rectangles_drawn = 0
        pages_modified = set()
        
        try:
            for pii_match, coordinates in pii_coordinate_mapping:
                for coord in coordinates:
                    page = doc.load_page(coord.page_num)
                    
                    # Create rectangle with padding
                    rect = fitz.Rect(
                        coord.x1 - self.padding,
                        coord.y1 - self.padding,
                        coord.x2 + self.padding,
                        coord.y2 + self.padding
                    )
                    
                    # Draw black rectangle
                    page.draw_rect(rect, color=(0, 0, 0), fill=(0, 0, 0))
                    rectangles_drawn += 1
                    pages_modified.add(coord.page_num)
                    
                    logger.debug(
                        f"Drew black box on page {coord.page_num} at {rect} for PII: {pii_match.text[:20]}..."
                    )
            
            # Save the modified PDF
            doc.save(str(output_path))
            doc.close()
            
            return {
                "rectangles_drawn": rectangles_drawn,
                "pages_modified": len(pages_modified),
                "method": "black_box"
            }
            
        except Exception as e:
            doc.close()
            raise e
    
    async def _apply_redaction_obfuscation(
        self, 
        input_path: Path, 
        output_path: Path, 
        pii_coordinate_mapping: List[Tuple[PIIMatch, List[TextCoordinate]]]
    ) -> Dict[str, any]:
        """Apply redaction obfuscation (complete text removal)."""
        doc = fitz.open(str(input_path))
        redactions_applied = 0
        pages_modified = set()
        
        try:
            for pii_match, coordinates in pii_coordinate_mapping:
                for coord in coordinates:
                    page = doc.load_page(coord.page_num)
                    
                    # Create redaction annotation
                    rect = fitz.Rect(
                        coord.x1 - self.padding,
                        coord.y1 - self.padding,
                        coord.x2 + self.padding,
                        coord.y2 + self.padding
                    )
                    
                    # Add redaction annotation
                    redact_annot = page.add_redact_annot(rect)
                    redactions_applied += 1
                    pages_modified.add(coord.page_num)
            
            # Apply all redactions
            for page_num in pages_modified:
                page = doc.load_page(page_num)
                page.apply_redactions()
            
            # Save the modified PDF
            doc.save(str(output_path))
            doc.close()
            
            return {
                "redactions_applied": redactions_applied,
                "pages_modified": len(pages_modified),
                "method": "redact"
            }
            
        except Exception as e:
            doc.close()
            raise e
    
    async def _apply_white_box_obfuscation(
        self, 
        input_path: Path, 
        output_path: Path, 
        pii_coordinate_mapping: List[Tuple[PIIMatch, List[TextCoordinate]]]
    ) -> Dict[str, any]:
        """Apply white box obfuscation."""
        doc = fitz.open(str(input_path))
        rectangles_drawn = 0
        pages_modified = set()
        
        try:
            for pii_match, coordinates in pii_coordinate_mapping:
                for coord in coordinates:
                    page = doc.load_page(coord.page_num)
                    
                    # Create rectangle with padding
                    rect = fitz.Rect(
                        coord.x1 - self.padding,
                        coord.y1 - self.padding,
                        coord.x2 + self.padding,
                        coord.y2 + self.padding
                    )
                    
                    # Draw white rectangle
                    page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
                    rectangles_drawn += 1
                    pages_modified.add(coord.page_num)
            
            # Save the modified PDF
            doc.save(str(output_path))
            doc.close()
            
            return {
                "rectangles_drawn": rectangles_drawn,
                "pages_modified": len(pages_modified),
                "method": "white_box"
            }
            
        except Exception as e:
            doc.close()
            raise e
    
    def create_obfuscation_report(
        self, 
        obfuscation_results: List[Dict[str, any]]
    ) -> Dict[str, any]:
        """Create a comprehensive obfuscation report.
        
        Args:
            obfuscation_results: List of obfuscation results
            
        Returns:
            Comprehensive report dictionary
        """
        total_files = len(obfuscation_results)
        successful_files = len([r for r in obfuscation_results if r.get("success", False)])
        
        total_pii_found = sum(r.get("pii_items_found", 0) for r in obfuscation_results)
        total_pii_obfuscated = sum(r.get("pii_items_obfuscated", 0) for r in obfuscation_results)
        
        # PII type breakdown
        pii_type_counts = {}
        for result in obfuscation_results:
            for coord_info in result.get("obfuscated_coordinates", []):
                pii_type = coord_info["pii_type"]
                pii_type_counts[pii_type] = pii_type_counts.get(pii_type, 0) + 1
        
        report = {
            "summary": {
                "total_files_processed": total_files,
                "successful_obfuscations": successful_files,
                "failed_obfuscations": total_files - successful_files,
                "success_rate": successful_files / total_files if total_files > 0 else 0,
                "total_pii_items_found": total_pii_found,
                "total_pii_items_obfuscated": total_pii_obfuscated,
                "obfuscation_rate": total_pii_obfuscated / total_pii_found if total_pii_found > 0 else 0
            },
            "pii_type_breakdown": pii_type_counts,
            "method_used": self.method.value,
            "detailed_results": obfuscation_results
        }
        
        return report