# Google Gemini API Configuration
GOOGLE_API_KEY=your_google_gemini_api_key_here

# Supabase Configuration  
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# OCR Processing Configuration
MAX_RETRIES=3
RETRY_DELAY=1.0
BATCH_SIZE=5
MAX_CONCURRENT_REQUESTS=10

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# File Processing Configuration
MAX_FILE_SIZE_MB=50
SUPPORTED_FORMATS=pdf
OUTPUT_DIR=./processed
TEMP_DIR=./temp

# PII Detection Configuration
CONFIDENCE_THRESHOLD=0.7
ENABLE_COORDINATE_TRACKING=true
OBFUSCATION_METHOD=black_box