# ChromoForge Supabase Architecture

## Overview

This document outlines the complete Supabase architecture for ChromoForge, a medical OCR application designed with security, compliance, and data protection as primary concerns.

## Database Schema

### 1. Enable Required Extensions

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For text search
CREATE EXTENSION IF NOT EXISTS "btree_gist"; -- For exclusion constraints
```

### 2. Custom Types

```sql
-- User role enum
CREATE TYPE user_role AS ENUM ('admin', 'editor', 'viewer', 'analyst');

-- Document status enum
CREATE TYPE document_status AS ENUM ('processing', 'completed', 'failed', 'archived');

-- Audit action types
CREATE TYPE audit_action AS ENUM (
  'create', 'read', 'update', 'delete', 'soft_delete', 
  'restore', 'export', 'login', 'logout', 'permission_change'
);
```

### 3. Core Tables

#### Organizations Table

```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  settings JSONB DEFAULT '{}',
  
  -- Constraints
  CONSTRAINT organizations_name_unique UNIQUE (name)
);

-- Indexes
CREATE INDEX idx_organizations_deleted_at ON organizations(deleted_at);
```

#### User Profiles Table

```sql
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id),
  role user_role NOT NULL DEFAULT 'viewer',
  full_name TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  last_login_at TIMESTAMPTZ,
  settings JSONB DEFAULT '{}',
  
  -- Constraints
  CONSTRAINT user_profiles_org_user_unique UNIQUE (organization_id, id)
);

-- Indexes
CREATE INDEX idx_user_profiles_organization_id ON user_profiles(organization_id);
CREATE INDEX idx_user_profiles_role ON user_profiles(role);
CREATE INDEX idx_user_profiles_deleted_at ON user_profiles(deleted_at);
```

#### Documents Table

```sql
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  uploaded_by UUID NOT NULL REFERENCES user_profiles(id),
  
  -- Document metadata
  file_name TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  mime_type TEXT NOT NULL,
  checksum TEXT NOT NULL, -- SHA-256 hash for integrity
  
  -- Storage paths (encrypted)
  original_path TEXT, -- Encrypted path to original PDF
  obfuscated_path TEXT, -- Path to redacted PDF
  
  -- Status tracking
  status document_status DEFAULT 'processing',
  processing_started_at TIMESTAMPTZ,
  processing_completed_at TIMESTAMPTZ,
  processing_error JSONB,
  
  -- Metadata
  page_count INTEGER,
  metadata JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  
  -- Constraints
  CONSTRAINT documents_checksum_unique UNIQUE (organization_id, checksum),
  CONSTRAINT documents_file_size_positive CHECK (file_size > 0)
);

-- Indexes
CREATE INDEX idx_documents_organization_id ON documents(organization_id);
CREATE INDEX idx_documents_uploaded_by ON documents(uploaded_by);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_created_at ON documents(created_at DESC);
CREATE INDEX idx_documents_deleted_at ON documents(deleted_at);
```

#### Medical Records Table (with Encrypted PII)

```sql
CREATE TABLE medical_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES documents(id),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  
  -- Encrypted PII fields (using pgcrypto)
  patient_name_encrypted BYTEA, -- Encrypted with organization key
  patient_id_encrypted BYTEA, -- Thai ID, encrypted
  hospital_number_encrypted BYTEA,
  lab_number_encrypted BYTEA,
  
  -- Non-PII medical data
  record_date DATE,
  record_type TEXT,
  department TEXT,
  
  -- Structured extracted data (non-PII)
  test_results JSONB DEFAULT '{}',
  diagnoses JSONB DEFAULT '[]',
  medications JSONB DEFAULT '[]',
  
  -- Search vectors for non-PII content
  search_vector tsvector,
  
  -- Metadata
  confidence_score DECIMAL(3,2),
  extraction_metadata JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  
  -- Constraints
  CONSTRAINT medical_records_confidence_range CHECK (confidence_score >= 0 AND confidence_score <= 1)
);

-- Indexes
CREATE INDEX idx_medical_records_document_id ON medical_records(document_id);
CREATE INDEX idx_medical_records_organization_id ON medical_records(organization_id);
CREATE INDEX idx_medical_records_record_date ON medical_records(record_date);
CREATE INDEX idx_medical_records_search_vector ON medical_records USING gin(search_vector);
CREATE INDEX idx_medical_records_deleted_at ON medical_records(deleted_at);
```

#### Audit Logs Table

```sql
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  user_id UUID REFERENCES user_profiles(id),
  
  -- Audit details
  action audit_action NOT NULL,
  table_name TEXT NOT NULL,
  record_id UUID,
  
  -- Change tracking
  old_values JSONB,
  new_values JSONB,
  
  -- Request metadata
  ip_address INET,
  user_agent TEXT,
  request_id UUID,
  
  -- Timestamp (immutable)
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  
  -- Constraints
  CONSTRAINT audit_logs_values_check CHECK (
    (action IN ('create', 'read') AND old_values IS NULL) OR
    (action IN ('update') AND old_values IS NOT NULL) OR
    (action NOT IN ('create', 'read', 'update'))
  )
);

-- Indexes (no update/delete allowed on audit logs)
CREATE INDEX idx_audit_logs_organization_id ON audit_logs(organization_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_table_name ON audit_logs(table_name);
CREATE INDEX idx_audit_logs_record_id ON audit_logs(record_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at DESC);

-- Make audit_logs table append-only
REVOKE UPDATE, DELETE ON audit_logs FROM PUBLIC;
```

#### Encryption Keys Table

```sql
CREATE TABLE encryption_keys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  key_name TEXT NOT NULL,
  encrypted_key BYTEA NOT NULL, -- Master key encrypted with Supabase vault
  created_at TIMESTAMPTZ DEFAULT NOW(),
  rotated_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT true,
  
  -- Constraints
  CONSTRAINT encryption_keys_org_name_unique UNIQUE (organization_id, key_name)
);

-- Indexes
CREATE INDEX idx_encryption_keys_organization_id ON encryption_keys(organization_id);
CREATE INDEX idx_encryption_keys_is_active ON encryption_keys(is_active);
```

### 4. Helper Functions

#### Encryption Functions

```sql
-- Function to get active encryption key for an organization
CREATE OR REPLACE FUNCTION get_encryption_key(org_id UUID)
RETURNS BYTEA AS $$
DECLARE
  key BYTEA;
BEGIN
  SELECT encrypted_key INTO key
  FROM encryption_keys
  WHERE organization_id = org_id
    AND is_active = true
    AND key_name = 'primary'
  LIMIT 1;
  
  IF key IS NULL THEN
    RAISE EXCEPTION 'No active encryption key found for organization';
  END IF;
  
  RETURN key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to encrypt PII data
CREATE OR REPLACE FUNCTION encrypt_pii(data TEXT, org_id UUID)
RETURNS BYTEA AS $$
DECLARE
  key BYTEA;
BEGIN
  key := get_encryption_key(org_id);
  RETURN pgp_sym_encrypt(data, key::TEXT);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrypt PII data
CREATE OR REPLACE FUNCTION decrypt_pii(encrypted_data BYTEA, org_id UUID)
RETURNS TEXT AS $$
DECLARE
  key BYTEA;
BEGIN
  key := get_encryption_key(org_id);
  RETURN pgp_sym_decrypt(encrypted_data, key::TEXT);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Audit Functions

```sql
-- Function to log audit events
CREATE OR REPLACE FUNCTION log_audit_event(
  p_action audit_action,
  p_table_name TEXT,
  p_record_id UUID,
  p_old_values JSONB DEFAULT NULL,
  p_new_values JSONB DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO audit_logs (
    organization_id,
    user_id,
    action,
    table_name,
    record_id,
    old_values,
    new_values,
    ip_address,
    user_agent,
    request_id
  )
  VALUES (
    auth.jwt() ->> 'organization_id',
    auth.uid(),
    p_action,
    p_table_name,
    p_record_id,
    p_old_values,
    p_new_values,
    current_setting('request.headers', true)::json ->> 'x-forwarded-for',
    current_setting('request.headers', true)::json ->> 'user-agent',
    current_setting('request.headers', true)::json ->> 'x-request-id'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger function for automatic audit logging
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    PERFORM log_audit_event(
      'create'::audit_action,
      TG_TABLE_NAME,
      NEW.id,
      NULL,
      to_jsonb(NEW)
    );
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    PERFORM log_audit_event(
      'update'::audit_action,
      TG_TABLE_NAME,
      NEW.id,
      to_jsonb(OLD),
      to_jsonb(NEW)
    );
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    PERFORM log_audit_event(
      'delete'::audit_action,
      TG_TABLE_NAME,
      OLD.id,
      to_jsonb(OLD),
      NULL
    );
    RETURN OLD;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Soft Delete Function

```sql
-- Function to handle soft deletes
CREATE OR REPLACE FUNCTION soft_delete(table_name TEXT, record_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  EXECUTE format('UPDATE %I SET deleted_at = NOW() WHERE id = $1 AND deleted_at IS NULL', table_name)
  USING record_id;
  
  PERFORM log_audit_event(
    'soft_delete'::audit_action,
    table_name,
    record_id,
    NULL,
    jsonb_build_object('deleted_at', NOW())
  );
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 5. Triggers

```sql
-- Update timestamp triggers
CREATE TRIGGER update_organizations_updated_at
  BEFORE UPDATE ON organizations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at
  BEFORE UPDATE ON documents
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_medical_records_updated_at
  BEFORE UPDATE ON medical_records
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Audit triggers
CREATE TRIGGER audit_organizations
  AFTER INSERT OR UPDATE OR DELETE ON organizations
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_documents
  AFTER INSERT OR UPDATE OR DELETE ON documents
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_medical_records
  AFTER INSERT OR UPDATE OR DELETE ON medical_records
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

-- Search vector update trigger
CREATE OR REPLACE FUNCTION update_medical_records_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector := 
    setweight(to_tsvector('english', COALESCE(NEW.record_type, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(NEW.department, '')), 'B') ||
    setweight(to_tsvector('english', COALESCE(NEW.diagnoses::text, '')), 'C');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_medical_records_search_vector_trigger
  BEFORE INSERT OR UPDATE ON medical_records
  FOR EACH ROW
  EXECUTE FUNCTION update_medical_records_search_vector();
```

## Row Level Security (RLS) Policies

### 1. Enable RLS on All Tables

```sql
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE encryption_keys ENABLE ROW LEVEL SECURITY;
```

### 2. Helper Functions for RLS

```sql
-- Get user's role
CREATE OR REPLACE FUNCTION get_user_role(user_id UUID)
RETURNS user_role AS $$
  SELECT role 
  FROM user_profiles 
  WHERE id = user_id AND deleted_at IS NULL
$$ LANGUAGE sql SECURITY DEFINER;

-- Get user's organization
CREATE OR REPLACE FUNCTION get_user_organization(user_id UUID)
RETURNS UUID AS $$
  SELECT organization_id 
  FROM user_profiles 
  WHERE id = user_id AND deleted_at IS NULL
$$ LANGUAGE sql SECURITY DEFINER;

-- Check if user has role
CREATE OR REPLACE FUNCTION has_role(user_id UUID, required_role user_role)
RETURNS BOOLEAN AS $$
DECLARE
  user_role_value user_role;
BEGIN
  user_role_value := get_user_role(user_id);
  
  -- Role hierarchy: admin > editor > viewer/analyst
  CASE required_role
    WHEN 'admin' THEN
      RETURN user_role_value = 'admin';
    WHEN 'editor' THEN
      RETURN user_role_value IN ('admin', 'editor');
    WHEN 'viewer' THEN
      RETURN user_role_value IN ('admin', 'editor', 'viewer');
    WHEN 'analyst' THEN
      RETURN user_role_value IN ('admin', 'analyst');
    ELSE
      RETURN FALSE;
  END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 3. Organizations RLS Policies

```sql
-- Admins can view their organization
CREATE POLICY "Users can view their organization"
  ON organizations FOR SELECT
  USING (
    id = get_user_organization(auth.uid())
    AND deleted_at IS NULL
  );

-- Only admins can update organization
CREATE POLICY "Admins can update organization"
  ON organizations FOR UPDATE
  USING (
    id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'admin')
    AND deleted_at IS NULL
  );
```

### 4. User Profiles RLS Policies

```sql
-- Users can view profiles in their organization
CREATE POLICY "Users can view profiles in organization"
  ON user_profiles FOR SELECT
  USING (
    organization_id = get_user_organization(auth.uid())
    AND deleted_at IS NULL
  );

-- Users can update their own profile
CREATE POLICY "Users can update own profile"
  ON user_profiles FOR UPDATE
  USING (id = auth.uid())
  WITH CHECK (
    id = auth.uid() 
    AND organization_id = get_user_organization(auth.uid())
  );

-- Admins can manage all profiles in organization
CREATE POLICY "Admins can manage profiles"
  ON user_profiles FOR ALL
  USING (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'admin')
  );
```

### 5. Documents RLS Policies

```sql
-- All authenticated users can view documents in their organization
CREATE POLICY "Users can view documents"
  ON documents FOR SELECT
  USING (
    organization_id = get_user_organization(auth.uid())
    AND deleted_at IS NULL
  );

-- Editors and admins can create documents
CREATE POLICY "Editors can create documents"
  ON documents FOR INSERT
  WITH CHECK (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'editor')
  );

-- Editors and admins can update documents
CREATE POLICY "Editors can update documents"
  ON documents FOR UPDATE
  USING (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'editor')
    AND deleted_at IS NULL
  );

-- Only admins can hard delete (soft delete via function)
CREATE POLICY "Admins can delete documents"
  ON documents FOR DELETE
  USING (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'admin')
  );
```

### 6. Medical Records RLS Policies

```sql
-- Role-based view permissions
CREATE POLICY "Users can view medical records"
  ON medical_records FOR SELECT
  USING (
    organization_id = get_user_organization(auth.uid())
    AND deleted_at IS NULL
    AND (
      -- Admins and editors see all fields
      has_role(auth.uid(), 'editor')
      OR
      -- Viewers see non-PII data only (handled at column level)
      has_role(auth.uid(), 'viewer')
      OR
      -- Analysts see anonymized data
      has_role(auth.uid(), 'analyst')
    )
  );

-- Editors can create medical records
CREATE POLICY "Editors can create medical records"
  ON medical_records FOR INSERT
  WITH CHECK (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'editor')
  );

-- Editors can update medical records
CREATE POLICY "Editors can update medical records"
  ON medical_records FOR UPDATE
  USING (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'editor')
    AND deleted_at IS NULL
  );
```

### 7. Audit Logs RLS Policies

```sql
-- Only admins can view audit logs
CREATE POLICY "Admins can view audit logs"
  ON audit_logs FOR SELECT
  USING (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'admin')
  );

-- Audit logs are inserted via function only
CREATE POLICY "System can insert audit logs"
  ON audit_logs FOR INSERT
  WITH CHECK (false); -- Only system functions can insert
```

### 8. Column-Level Security for PII

```sql
-- Create views with column-level security
CREATE OR REPLACE VIEW medical_records_secure AS
SELECT 
  id,
  document_id,
  organization_id,
  -- Decrypt PII only for authorized roles
  CASE 
    WHEN has_role(auth.uid(), 'editor') THEN 
      decrypt_pii(patient_name_encrypted, organization_id)
    ELSE NULL 
  END as patient_name,
  CASE 
    WHEN has_role(auth.uid(), 'editor') THEN 
      decrypt_pii(patient_id_encrypted, organization_id)
    ELSE NULL 
  END as patient_id,
  CASE 
    WHEN has_role(auth.uid(), 'editor') THEN 
      decrypt_pii(hospital_number_encrypted, organization_id)
    ELSE NULL 
  END as hospital_number,
  CASE 
    WHEN has_role(auth.uid(), 'editor') THEN 
      decrypt_pii(lab_number_encrypted, organization_id)
    ELSE NULL 
  END as lab_number,
  -- Non-PII fields available to all
  record_date,
  record_type,
  department,
  test_results,
  diagnoses,
  medications,
  confidence_score,
  extraction_metadata,
  created_at,
  updated_at,
  deleted_at
FROM medical_records
WHERE deleted_at IS NULL;

-- Grant appropriate permissions
GRANT SELECT ON medical_records_secure TO authenticated;
```

## Storage Configuration

### 1. Storage Buckets

```sql
-- Original documents bucket (highly restricted)
INSERT INTO storage.buckets (id, name, public, avif_autodetection, allowed_mime_types)
VALUES (
  'original-documents',
  'original-documents',
  false,
  false,
  ARRAY['application/pdf']
);

-- Obfuscated documents bucket
INSERT INTO storage.buckets (id, name, public, avif_autodetection, allowed_mime_types)
VALUES (
  'obfuscated-documents',
  'obfuscated-documents',
  false,
  false,
  ARRAY['application/pdf']
);
```

### 2. Storage Policies

#### Original Documents Policies

```sql
-- Only editors and admins can upload original documents
CREATE POLICY "Editors can upload original documents"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'original-documents'
    AND has_role(auth.uid(), 'editor')
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );

-- Only editors and admins can view original documents
CREATE POLICY "Editors can view original documents"
  ON storage.objects FOR SELECT
  USING (
    bucket_id = 'original-documents'
    AND has_role(auth.uid(), 'editor')
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );

-- Only admins can delete original documents
CREATE POLICY "Admins can delete original documents"
  ON storage.objects FOR DELETE
  USING (
    bucket_id = 'original-documents'
    AND has_role(auth.uid(), 'admin')
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );
```

#### Obfuscated Documents Policies

```sql
-- All authenticated users can view obfuscated documents in their org
CREATE POLICY "Users can view obfuscated documents"
  ON storage.objects FOR SELECT
  USING (
    bucket_id = 'obfuscated-documents'
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );

-- Only editors can upload obfuscated documents
CREATE POLICY "Editors can upload obfuscated documents"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'obfuscated-documents'
    AND has_role(auth.uid(), 'editor')
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );
```

## Authentication Setup

### 1. Custom Claims

```sql
-- Function to set custom claims on JWT
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Set default role and organization
  INSERT INTO user_profiles (id, organization_id, role)
  VALUES (
    NEW.id,
    COALESCE(
      (NEW.raw_user_meta_data->>'organization_id')::UUID,
      (SELECT id FROM organizations WHERE name = 'Default' LIMIT 1)
    ),
    COALESCE(
      (NEW.raw_user_meta_data->>'role')::user_role,
      'viewer'::user_role
    )
  );
  
  -- Update JWT claims
  UPDATE auth.users
  SET raw_app_meta_data = raw_app_meta_data || 
    jsonb_build_object(
      'organization_id', 
      (SELECT organization_id FROM user_profiles WHERE id = NEW.id),
      'role',
      (SELECT role FROM user_profiles WHERE id = NEW.id)
    )
  WHERE id = NEW.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user();
```

### 2. JWT Hook for Custom Claims

```sql
-- Function to add custom claims to JWT
CREATE OR REPLACE FUNCTION custom_access_token_hook(event jsonb)
RETURNS jsonb AS $$
DECLARE
  claims jsonb;
  user_organization_id UUID;
  user_role user_role;
BEGIN
  -- Get user's current organization and role
  SELECT organization_id, role INTO user_organization_id, user_role
  FROM user_profiles
  WHERE id = (event->>'user_id')::UUID
  AND deleted_at IS NULL;
  
  claims := event->'claims';
  
  -- Add custom claims
  claims := jsonb_set(claims, '{organization_id}', to_jsonb(user_organization_id));
  claims := jsonb_set(claims, '{role}', to_jsonb(user_role));
  
  -- Return modified event
  RETURN jsonb_set(event, '{claims}', claims);
END;
$$ LANGUAGE plpgsql;

-- Configure the hook in Supabase dashboard
```

## Medical Data Compliance Best Practices

### 1. Data Retention Policy

```sql
-- Function to anonymize old records
CREATE OR REPLACE FUNCTION anonymize_old_records()
RETURNS void AS $$
BEGIN
  -- Anonymize records older than retention period (e.g., 7 years)
  UPDATE medical_records
  SET 
    patient_name_encrypted = NULL,
    patient_id_encrypted = NULL,
    hospital_number_encrypted = NULL,
    lab_number_encrypted = NULL,
    extraction_metadata = extraction_metadata || jsonb_build_object('anonymized_at', NOW())
  WHERE created_at < NOW() - INTERVAL '7 years'
    AND patient_name_encrypted IS NOT NULL;
    
  -- Log the anonymization
  INSERT INTO audit_logs (action, table_name, new_values)
  SELECT 
    'update'::audit_action,
    'medical_records',
    jsonb_build_object('count', COUNT(*), 'anonymized_at', NOW())
  FROM medical_records
  WHERE created_at < NOW() - INTERVAL '7 years';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Schedule this function to run monthly
```

### 2. Data Export for Compliance

```sql
-- Function to export user data for GDPR/HIPAA compliance
CREATE OR REPLACE FUNCTION export_user_data(patient_id_to_export TEXT)
RETURNS TABLE (
  document_name TEXT,
  record_date DATE,
  record_type TEXT,
  data JSONB
) AS $$
DECLARE
  org_id UUID;
BEGIN
  -- Get organization context
  org_id := get_user_organization(auth.uid());
  
  -- Only allow if user has appropriate permissions
  IF NOT has_role(auth.uid(), 'admin') THEN
    RAISE EXCEPTION 'Insufficient permissions for data export';
  END IF;
  
  -- Log the export
  PERFORM log_audit_event(
    'export'::audit_action,
    'medical_records',
    NULL,
    NULL,
    jsonb_build_object('patient_id', patient_id_to_export)
  );
  
  -- Return anonymized data
  RETURN QUERY
  SELECT 
    d.file_name,
    m.record_date,
    m.record_type,
    jsonb_build_object(
      'test_results', m.test_results,
      'diagnoses', m.diagnoses,
      'medications', m.medications
    )
  FROM medical_records m
  JOIN documents d ON m.document_id = d.id
  WHERE m.organization_id = org_id
    AND decrypt_pii(m.patient_id_encrypted, org_id) = patient_id_to_export
    AND m.deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 3. Access Monitoring

```sql
-- Function to detect suspicious access patterns
CREATE OR REPLACE FUNCTION check_suspicious_access()
RETURNS TABLE (
  user_id UUID,
  suspicious_activity TEXT,
  event_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  -- Check for excessive data access
  SELECT 
    al.user_id,
    'Excessive data access in last hour' as suspicious_activity,
    COUNT(*) as event_count
  FROM audit_logs al
  WHERE al.action = 'read'
    AND al.created_at > NOW() - INTERVAL '1 hour'
  GROUP BY al.user_id
  HAVING COUNT(*) > 1000
  
  UNION ALL
  
  -- Check for access outside business hours
  SELECT 
    al.user_id,
    'Access outside business hours' as suspicious_activity,
    COUNT(*) as event_count
  FROM audit_logs al
  WHERE EXTRACT(HOUR FROM al.created_at) NOT BETWEEN 6 AND 22
    AND al.created_at > NOW() - INTERVAL '24 hours'
  GROUP BY al.user_id
  HAVING COUNT(*) > 10;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 4. Encryption Key Rotation

```sql
-- Function to rotate encryption keys
CREATE OR REPLACE FUNCTION rotate_encryption_keys(org_id UUID)
RETURNS void AS $$
DECLARE
  old_key BYTEA;
  new_key BYTEA;
  record RECORD;
BEGIN
  -- Get current key
  old_key := get_encryption_key(org_id);
  
  -- Generate new key
  new_key := gen_random_bytes(32);
  
  -- Deactivate old key
  UPDATE encryption_keys
  SET is_active = false
  WHERE organization_id = org_id
    AND is_active = true;
  
  -- Insert new key
  INSERT INTO encryption_keys (organization_id, key_name, encrypted_key, is_active)
  VALUES (org_id, 'primary', new_key, true);
  
  -- Re-encrypt all PII data with new key
  FOR record IN 
    SELECT id, patient_name_encrypted, patient_id_encrypted, 
           hospital_number_encrypted, lab_number_encrypted
    FROM medical_records
    WHERE organization_id = org_id
      AND deleted_at IS NULL
  LOOP
    UPDATE medical_records
    SET 
      patient_name_encrypted = pgp_sym_encrypt(
        pgp_sym_decrypt(record.patient_name_encrypted, old_key::TEXT),
        new_key::TEXT
      ),
      patient_id_encrypted = pgp_sym_encrypt(
        pgp_sym_decrypt(record.patient_id_encrypted, old_key::TEXT),
        new_key::TEXT
      ),
      hospital_number_encrypted = pgp_sym_encrypt(
        pgp_sym_decrypt(record.hospital_number_encrypted, old_key::TEXT),
        new_key::TEXT
      ),
      lab_number_encrypted = pgp_sym_encrypt(
        pgp_sym_decrypt(record.lab_number_encrypted, old_key::TEXT),
        new_key::TEXT
      )
    WHERE id = record.id;
  END LOOP;
  
  -- Log key rotation
  PERFORM log_audit_event(
    'update'::audit_action,
    'encryption_keys',
    NULL,
    NULL,
    jsonb_build_object('action', 'key_rotation', 'organization_id', org_id)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Implementation Checklist

### Initial Setup
- [ ] Enable required PostgreSQL extensions
- [ ] Create custom types and enums
- [ ] Create all tables with proper constraints
- [ ] Set up encryption keys for each organization
- [ ] Configure storage buckets
- [ ] Enable RLS on all tables
- [ ] Create and apply all RLS policies
- [ ] Set up authentication hooks
- [ ] Configure audit triggers

### Security Hardening
- [ ] Implement API rate limiting
- [ ] Set up SSL/TLS for all connections
- [ ] Configure CORS policies appropriately
- [ ] Enable query performance monitoring
- [ ] Set up automated backups with encryption
- [ ] Implement key rotation schedule
- [ ] Configure intrusion detection
- [ ] Set up compliance reporting

### Monitoring & Maintenance
- [ ] Set up alerts for suspicious access patterns
- [ ] Monitor storage usage and costs
- [ ] Schedule regular security audits
- [ ] Implement automated testing for RLS policies
- [ ] Monitor query performance
- [ ] Set up error tracking and logging
- [ ] Create compliance dashboards
- [ ] Document all procedures

## Security Considerations

1. **Encryption at Rest**: All PII data is encrypted using pgcrypto with organization-specific keys
2. **Encryption in Transit**: Enforce SSL/TLS for all database connections
3. **Access Control**: Role-based access with granular permissions
4. **Audit Trail**: Immutable audit logs for all data access and modifications
5. **Data Isolation**: Organization-based data isolation with RLS
6. **Key Management**: Secure key storage with rotation capabilities
7. **Soft Deletes**: Preserve data integrity while allowing recovery
8. **Compliance**: HIPAA-compliant design with data retention and export capabilities

## Performance Optimizations

1. **Indexes**: Strategic indexes on frequently queried columns
2. **Partitioning**: Consider partitioning audit_logs by created_at for large datasets
3. **Connection Pooling**: Use PgBouncer for connection management
4. **Query Optimization**: Regular EXPLAIN ANALYZE on critical queries
5. **Caching**: Implement Redis for frequently accessed, non-sensitive data
6. **Batch Processing**: Process OCR jobs in batches during off-peak hours

This architecture provides a secure, compliant, and scalable foundation for the ChromoForge medical OCR application.