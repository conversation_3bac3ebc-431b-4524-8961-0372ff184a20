"""Test configuration and fixtures for real integration testing."""

import asyncio
import os
import tempfile
from pathlib import Path
from typing import Generator, List
import pytest
import logging

from src.ocr_processor import GeminiOCRProcessor
from src.pii_detector import PIIDetector  
from src.pdf_obfuscator import PDFObfuscator
from src.batch_processor import BatchProcessor
from src.config import settings

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def real_pdf_samples() -> List[Path]:
    """Get list of real PDF samples for testing.
    
    Returns:
        List of paths to actual PDF files in original-pdf-examples/
    """
    samples_dir = Path(__file__).parent.parent / "original-pdf-examples"
    
    if not samples_dir.exists():
        pytest.skip("PDF samples directory not found - cannot run real integration tests")
    
    pdf_files = list(samples_dir.glob("*.pdf"))
    
    if not pdf_files:
        pytest.skip("No PDF samples found - cannot run real integration tests")
    
    logger.info(f"Found {len(pdf_files)} real PDF samples for testing")
    return pdf_files


@pytest.fixture(scope="session")
def gemini_api_key() -> str:
    """Get real Gemini API key for testing.
    
    Returns:
        Valid Gemini API key
        
    Raises:
        pytest.skip: If API key not available
    """
    api_key = os.getenv("GOOGLE_API_KEY") or settings.google_api_key
    
    if not api_key or api_key == "your_google_gemini_api_key_here":
        pytest.skip("Real Gemini API key required for integration tests")
    
    logger.info("Using real Gemini API key for testing")
    return api_key


@pytest.fixture(scope="session")
def supabase_credentials() -> dict:
    """Get real Supabase credentials for testing.
    
    Returns:
        Dictionary with Supabase URL and keys
        
    Raises:
        pytest.skip: If credentials not available
    """
    url = os.getenv("NEXT_PUBLIC_SUPABASE_URL") or settings.supabase_url
    anon_key = os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY") or settings.supabase_anon_key
    service_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or settings.supabase_service_key
    
    if (not url or not anon_key or not service_key or 
        "your-project" in url or "your_" in anon_key):
        pytest.skip("Real Supabase credentials required for integration tests")
    
    logger.info(f"Using real Supabase instance: {url}")
    return {
        "url": url,
        "anon_key": anon_key,
        "service_key": service_key
    }


@pytest.fixture
def real_ocr_processor(gemini_api_key: str) -> GeminiOCRProcessor:
    """Create OCR processor with real API credentials.
    
    Args:
        gemini_api_key: Real Gemini API key
        
    Returns:
        Configured OCR processor for real API calls
    """
    # Temporarily override API key for testing
    original_key = settings.google_api_key
    settings.google_api_key = gemini_api_key
    
    processor = GeminiOCRProcessor()
    
    # Restore original key after initialization
    settings.google_api_key = original_key
    
    return processor


@pytest.fixture
def real_pii_detector() -> PIIDetector:
    """Create PII detector with production configuration.
    
    Returns:
        Configured PII detector for real text analysis
    """
    return PIIDetector(confidence_threshold=0.7)


@pytest.fixture
def real_pdf_obfuscator() -> PDFObfuscator:
    """Create PDF obfuscator for real PDF manipulation.
    
    Returns:
        Configured PDF obfuscator for real coordinate-based masking
    """
    return PDFObfuscator()


@pytest.fixture
def real_batch_processor(
    real_ocr_processor: GeminiOCRProcessor,
    real_pii_detector: PIIDetector,
    real_pdf_obfuscator: PDFObfuscator
) -> BatchProcessor:
    """Create batch processor with real components.
    
    Args:
        real_ocr_processor: Real OCR processor
        real_pii_detector: Real PII detector
        real_pdf_obfuscator: Real PDF obfuscator
        
    Returns:
        Configured batch processor for real concurrent processing
    """
    return BatchProcessor(
        ocr_processor=real_ocr_processor,
        pii_detector=real_pii_detector,
        pdf_obfuscator=real_pdf_obfuscator
    )


@pytest.fixture
def temp_output_dir() -> Generator[Path, None, None]:
    """Create temporary directory for test outputs.
    
    Yields:
        Path to temporary directory
    """
    with tempfile.TemporaryDirectory() as temp_dir:
        output_path = Path(temp_dir)
        logger.info(f"Created temporary output directory: {output_path}")
        yield output_path


@pytest.fixture
def sample_thai_medical_pdf(real_pdf_samples: List[Path]) -> Path:
    """Get a Thai medical PDF sample for focused testing.
    
    Args:
        real_pdf_samples: List of available PDF samples
        
    Returns:
        Path to Thai medical PDF sample
    """
    # Prefer CSF files as they are likely medical documents
    thai_samples = [p for p in real_pdf_samples if "CSF" in p.name]
    
    if thai_samples:
        return thai_samples[0]
    else:
        return real_pdf_samples[0]


@pytest.fixture
def performance_test_samples(real_pdf_samples: List[Path]) -> List[Path]:
    """Get subset of PDF samples for performance testing.
    
    Args:
        real_pdf_samples: List of available PDF samples
        
    Returns:
        List of PDF samples for performance validation
    """
    # Return first 5 samples for performance testing
    return real_pdf_samples[:5]


# Test configuration
pytestmark = pytest.mark.asyncio


def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "integration: Real integration tests with external services"
    )
    config.addinivalue_line(
        "markers", "performance: Performance validation tests"
    )
    config.addinivalue_line(
        "markers", "security: Security validation tests"
    )
    config.addinivalue_line(
        "markers", "slow: Slow tests that may take several minutes"
    )


def pytest_collection_modifyitems(config, items):
    """Add markers to test items based on their location."""
    for item in items:
        # Mark all tests in this suite as integration tests
        item.add_marker(pytest.mark.integration)
        
        # Mark specific test types
        if "performance" in item.name or "benchmark" in item.name:
            item.add_marker(pytest.mark.performance)
            item.add_marker(pytest.mark.slow)
            
        if "security" in item.name or "pii" in item.name:
            item.add_marker(pytest.mark.security)
            
        if "batch" in item.name or "concurrent" in item.name:
            item.add_marker(pytest.mark.slow)