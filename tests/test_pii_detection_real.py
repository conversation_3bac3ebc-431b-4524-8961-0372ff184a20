"""Real PII detection tests with Thai medical documents - NO MOCKS.

These tests use actual Thai medical PDF samples to validate PII detection accuracy,
confidence scoring, and edge case handling with real-world data.
"""

import pytest
from pathlib import Path
from typing import List

from src.pii_detector import PIIDetector, PIIType
from src.ocr_processor import GeminiOCRProcessor


@pytest.mark.integration
@pytest.mark.security
@pytest.mark.asyncio
class TestRealPIIDetection:
    """Real PII detection test suite with actual medical documents."""
    
    async def test_real_thai_id_detection(
        self,
        real_pii_detector: PIIDetector,
        real_ocr_processor: GeminiOCRProcessor,
        sample_thai_medical_pdf: Path
    ):
        """Test Thai National ID detection with real medical document.
        
        Validates:
        - Thai ID pattern recognition
        - Checksum validation accuracy  
        - Confidence scoring for Thai IDs
        - Context-aware detection
        """
        # Extract text from real PDF
        ocr_result = await real_ocr_processor.process_pdf_with_retry(sample_thai_medical_pdf)
        assert not ocr_result.errors, f"OCR should succeed: {ocr_result.errors}"
        
        # Detect PII in extracted text
        pii_matches = real_pii_detector.detect_pii(ocr_result.full_text)
        
        # Find Thai ID matches
        thai_id_matches = [match for match in pii_matches if match.pii_type == PIIType.THAI_ID]
        
        print(f"Found {len(thai_id_matches)} Thai ID candidates in {sample_thai_medical_pdf.name}")
        
        for match in thai_id_matches:
            print(f"  Thai ID: '{match.text}' (confidence: {match.confidence:.3f})")
            
            # Validate Thai ID format
            digits_only = ''.join(filter(str.isdigit, match.text))
            assert len(digits_only) == 13, \
                f"Thai ID should have 13 digits, got {len(digits_only)} in '{match.text}'"
            
            # Validate confidence scoring
            assert 0.0 <= match.confidence <= 1.0, \
                f"Confidence should be 0.0-1.0, got {match.confidence}"
            
            # High confidence Thai IDs should have valid checksums
            if match.confidence > 0.8:
                assert self._validate_thai_id_checksum(digits_only), \
                    f"High confidence Thai ID should have valid checksum: {match.text}"
            
            # Validate context extraction
            assert match.context, "Should extract context around Thai ID"
            assert len(match.context) > len(match.text), \
                "Context should be longer than the matched text"
        
        # Document should have at least some identification numbers
        if not thai_id_matches:
            print("⚠ No Thai IDs detected - this may be expected for some documents")
    
    def _validate_thai_id_checksum(self, thai_id: str) -> bool:
        """Validate Thai ID checksum algorithm."""
        if len(thai_id) != 13 or not thai_id.isdigit():
            return False
        
        try:
            digits = [int(d) for d in thai_id[:12]]
            check_digit = int(thai_id[12])
            sum_digits = sum((13 - i) * digit for i, digit in enumerate(digits))
            calculated_check = (11 - (sum_digits % 11)) % 10
            return calculated_check == check_digit
        except (ValueError, IndexError):
            return False
    
    async def test_real_thai_name_extraction(
        self,
        real_pii_detector: PIIDetector,
        real_ocr_processor: GeminiOCRProcessor,
        sample_thai_medical_pdf: Path
    ):
        """Test Thai name detection with real medical document.
        
        Validates:
        - Thai Unicode character recognition
        - Name pattern matching
        - Title detection (นาย, นาง, นางสาว)
        - Context-based confidence scoring
        """
        # Extract text from real PDF
        ocr_result = await real_ocr_processor.process_pdf_with_retry(sample_thai_medical_pdf)
        assert not ocr_result.errors, f"OCR should succeed: {ocr_result.errors}"
        
        # Check if document contains Thai text
        has_thai_text = any('\u0E00' <= char <= '\u0E7F' for char in ocr_result.full_text)
        
        if not has_thai_text:
            print("⚠ No Thai Unicode characters found - skipping Thai name test")
            return
        
        # Detect PII in extracted text
        pii_matches = real_pii_detector.detect_pii(ocr_result.full_text)
        
        # Find Thai name matches
        thai_name_matches = [match for match in pii_matches if match.pii_type == PIIType.THAI_NAME]
        
        print(f"Found {len(thai_name_matches)} Thai name candidates in {sample_thai_medical_pdf.name}")
        
        for match in thai_name_matches:
            print(f"  Thai Name: '{match.text}' (confidence: {match.confidence:.3f})")
            
            # Validate Thai characters presence
            has_thai_chars = any('\u0E00' <= char <= '\u0E7F' for char in match.text)
            assert has_thai_chars, f"Thai name should contain Thai characters: {match.text}"
            
            # Validate reasonable length
            assert 2 <= len(match.text.split()) <= 6, \
                f"Thai name should have 2-6 words, got {len(match.text.split())} in '{match.text}'"
            
            # Check for common Thai titles
            thai_titles = ['นาย', 'นาง', 'นางสาว', 'ด.ช.', 'ด.ญ.']
            has_title = any(match.text.startswith(title) for title in thai_titles)
            
            if has_title:
                assert match.confidence > 0.7, \
                    f"Names with titles should have high confidence, got {match.confidence}"
            
            # Validate context
            assert match.context, "Should extract context around Thai name"
        
        if not thai_name_matches:
            print("⚠ No Thai names detected - this may be expected for some documents")
    
    async def test_real_hospital_number_extraction(
        self,
        real_pii_detector: PIIDetector,
        real_ocr_processor: GeminiOCRProcessor,
        sample_thai_medical_pdf: Path
    ):
        """Test hospital number detection with real medical document.
        
        Validates:
        - HN pattern recognition
        - Hospital number format validation
        - High confidence scoring for explicit patterns
        - Medical context detection
        """
        # Extract text from real PDF
        ocr_result = await real_ocr_processor.process_pdf_with_retry(sample_thai_medical_pdf)
        assert not ocr_result.errors, f"OCR should succeed: {ocr_result.errors}"
        
        # Detect PII in extracted text
        pii_matches = real_pii_detector.detect_pii(ocr_result.full_text)
        
        # Find hospital number matches
        hn_matches = [match for match in pii_matches if match.pii_type == PIIType.HOSPITAL_NUMBER]
        
        print(f"Found {len(hn_matches)} hospital number candidates in {sample_thai_medical_pdf.name}")
        
        for match in hn_matches:
            print(f"  Hospital Number: '{match.text}' (confidence: {match.confidence:.3f})")
            
            # Hospital numbers should have high confidence (explicit patterns)
            assert match.confidence > 0.8, \
                f"Hospital numbers should have high confidence, got {match.confidence}"
            
            # Should contain digits
            has_digits = any(char.isdigit() for char in match.text)
            assert has_digits, f"Hospital number should contain digits: {match.text}"
            
            # Should contain HN-related keywords
            hn_keywords = ['HN', 'H.N', 'Hospital', 'hospital']
            has_keyword = any(keyword in match.text for keyword in hn_keywords)
            assert has_keyword, f"Hospital number should contain HN keyword: {match.text}"
            
            # Validate context
            assert match.context, "Should extract context around hospital number"
            
            # Context should contain medical terminology
            medical_keywords = ['patient', 'admission', 'record', 'medical', 'hospital', 
                              'คนไข้', 'โรงพยาบาล', 'ผู้ป่วย']
            context_lower = match.context.lower()
            has_medical_context = any(keyword in context_lower for keyword in medical_keywords)
            
            if has_medical_context:
                print(f"    ✓ Medical context detected in: {match.context[:50]}...")
        
        # Medical documents should typically have hospital numbers
        if not hn_matches:
            print("⚠ No hospital numbers detected - checking for related patterns")
            
            # Look for number patterns that might be hospital numbers
            import re
            number_patterns = re.findall(r'\b\d{4,12}\b', ocr_result.full_text)
            print(f"  Found {len(number_patterns)} numeric patterns that could be hospital numbers")
    
    async def test_real_lab_number_extraction(
        self,
        real_pii_detector: PIIDetector,
        real_ocr_processor: GeminiOCRProcessor,
        sample_thai_medical_pdf: Path
    ):
        """Test laboratory number detection with real medical document.
        
        Validates:
        - Lab number pattern recognition
        - Specimen ID detection
        - Alphanumeric format validation
        - Laboratory context detection
        """
        # Extract text from real PDF
        ocr_result = await real_ocr_processor.process_pdf_with_retry(sample_thai_medical_pdf)
        assert not ocr_result.errors, f"OCR should succeed: {ocr_result.errors}"
        
        # Detect PII in extracted text
        pii_matches = real_pii_detector.detect_pii(ocr_result.full_text)
        
        # Find lab number matches
        lab_matches = [match for match in pii_matches if match.pii_type == PIIType.LAB_NUMBER]
        
        print(f"Found {len(lab_matches)} lab number candidates in {sample_thai_medical_pdf.name}")
        
        for match in lab_matches:
            print(f"  Lab Number: '{match.text}' (confidence: {match.confidence:.3f})")
            
            # Lab numbers should have high confidence (explicit patterns) 
            assert match.confidence > 0.8, \
                f"Lab numbers should have high confidence, got {match.confidence}"
            
            # Should contain alphanumeric characters
            has_alnum = any(char.isalnum() for char in match.text)
            assert has_alnum, f"Lab number should contain alphanumeric chars: {match.text}"
            
            # Should contain lab-related keywords
            lab_keywords = ['LN', 'L.N', 'Lab', 'laboratory', 'Specimen', 'spec', 'Test']
            has_keyword = any(keyword.lower() in match.text.lower() for keyword in lab_keywords)
            assert has_keyword, f"Lab number should contain lab keyword: {match.text}"
            
            # Validate context
            assert match.context, "Should extract context around lab number"
        
        if not lab_matches:
            print("⚠ No lab numbers detected - this may be expected for some documents")
    
    async def test_real_confidence_scoring_accuracy(
        self,
        real_pii_detector: PIIDetector,
        real_ocr_processor: GeminiOCRProcessor,
        performance_test_samples: List[Path]
    ):
        """Test confidence scoring accuracy across multiple real documents.
        
        Validates:
        - Confidence distribution patterns
        - High confidence correlation with explicit patterns  
        - Low confidence for ambiguous matches
        - Consistency across documents
        """
        confidence_by_type = {}
        total_matches = 0
        
        for pdf_path in performance_test_samples:
            print(f"Analyzing confidence in {pdf_path.name}")
            
            # Extract text
            ocr_result = await real_ocr_processor.process_pdf_with_retry(pdf_path)
            if ocr_result.errors:
                print(f"  ✗ OCR failed: {ocr_result.errors}")
                continue
            
            # Detect PII
            pii_matches = real_pii_detector.detect_pii(ocr_result.full_text)
            
            for match in pii_matches:
                pii_type = match.pii_type.value
                if pii_type not in confidence_by_type:
                    confidence_by_type[pii_type] = []
                
                confidence_by_type[pii_type].append(match.confidence)
                total_matches += 1
                
                print(f"  {pii_type}: {match.confidence:.3f} - '{match.text[:30]}...'")
        
        print(f"\nConfidence analysis across {len(performance_test_samples)} documents:")
        print(f"Total PII matches: {total_matches}")
        
        for pii_type, confidences in confidence_by_type.items():
            if confidences:
                avg_conf = sum(confidences) / len(confidences)
                min_conf = min(confidences)
                max_conf = max(confidences)
                
                print(f"\n{pii_type}:")
                print(f"  Count: {len(confidences)}")
                print(f"  Average: {avg_conf:.3f}")
                print(f"  Range: {min_conf:.3f} - {max_conf:.3f}")
                
                # Validate confidence ranges by PII type
                if pii_type in ['hospital_number', 'lab_number']:
                    # Explicit patterns should have high confidence
                    assert avg_conf > 0.8, \
                        f"{pii_type} should have high average confidence, got {avg_conf:.3f}"
                elif pii_type in ['thai_name', 'english_name']:
                    # Names have more variation but should be reasonable
                    assert avg_conf > 0.4, \
                        f"{pii_type} should have reasonable confidence, got {avg_conf:.3f}"
                
                # All confidences should be in valid range
                assert all(0.0 <= conf <= 1.0 for conf in confidences), \
                    f"All confidences should be 0.0-1.0 for {pii_type}"
        
        assert total_matches > 0, "Should detect some PII across test documents"
    
    async def test_real_edge_cases_with_damaged_pdfs(
        self,
        real_pii_detector: PIIDetector,
        real_ocr_processor: GeminiOCRProcessor,
        real_pdf_samples: List[Path]
    ):
        """Test PII detection with challenging real-world documents.
        
        Validates:
        - Handling of poor OCR quality
        - Partial text extraction
        - Damaged or corrupted characters
        - Graceful degradation
        """
        edge_case_results = []
        
        for pdf_path in real_pdf_samples:
            # Process PDF and analyze OCR quality
            ocr_result = await real_ocr_processor.process_pdf_with_retry(pdf_path)
            
            if ocr_result.errors:
                print(f"✗ {pdf_path.name}: OCR failed - {ocr_result.errors}")
                continue
            
            # Check for indicators of poor OCR quality
            text = ocr_result.full_text
            
            # Calculate quality indicators
            char_count = len(text)
            digit_ratio = sum(1 for c in text if c.isdigit()) / max(char_count, 1)
            alpha_ratio = sum(1 for c in text if c.isalpha()) / max(char_count, 1)
            special_ratio = sum(1 for c in text if not c.isalnum() and not c.isspace()) / max(char_count, 1)
            
            quality_score = alpha_ratio + digit_ratio - (special_ratio * 2)
            
            # Detect PII regardless of quality
            pii_matches = real_pii_detector.detect_pii(text)
            
            result = {
                'file': pdf_path.name,
                'text_length': char_count,
                'quality_score': quality_score,
                'confidence': ocr_result.confidence_score,
                'pii_count': len(pii_matches),
                'avg_pii_confidence': sum(m.confidence for m in pii_matches) / max(len(pii_matches), 1)
            }
            
            edge_case_results.append(result)
            
            print(f"{'✓' if quality_score > 0.5 else '?'} {pdf_path.name}:")
            print(f"  Quality score: {quality_score:.3f}")
            print(f"  OCR confidence: {ocr_result.confidence_score:.3f}")
            print(f"  PII detected: {len(pii_matches)}")
            print(f"  Avg PII confidence: {result['avg_pii_confidence']:.3f}")
            
            # Validate PII detection works even with poor quality
            if quality_score < 0.3 and len(pii_matches) > 0:
                print(f"  ✓ PII detection working despite poor OCR quality")
                
                # Check that low OCR quality correlates with lower PII confidence
                if result['avg_pii_confidence'] > 0.8:
                    print(f"  ⚠ High PII confidence despite poor OCR quality")
        
        # Analyze results
        if edge_case_results:
            avg_quality = sum(r['quality_score'] for r in edge_case_results) / len(edge_case_results)
            total_pii = sum(r['pii_count'] for r in edge_case_results)
            
            print(f"\nEdge case analysis:")
            print(f"  Documents processed: {len(edge_case_results)}")
            print(f"  Average quality score: {avg_quality:.3f}")
            print(f"  Total PII detected: {total_pii}")
            
            # Should detect some PII even in challenging conditions
            assert total_pii > 0, "Should detect some PII across challenging documents"
    
    async def test_real_pii_location_tracking(
        self,
        real_pii_detector: PIIDetector,
        real_ocr_processor: GeminiOCRProcessor,
        sample_thai_medical_pdf: Path
    ):
        """Test PII location tracking for obfuscation coordinates.
        
        Validates:
        - Line number tracking
        - Character position accuracy
        - Context extraction
        - Coordinate preparation for obfuscation
        """
        # Extract text from real PDF
        ocr_result = await real_ocr_processor.process_pdf_with_retry(sample_thai_medical_pdf)
        assert not ocr_result.errors, f"OCR should succeed: {ocr_result.errors}"
        
        # Detect PII with location tracking
        pii_matches = real_pii_detector.detect_pii(ocr_result.full_text, include_coordinates=True)
        
        text_lines = ocr_result.full_text.split('\n')
        
        for match in pii_matches:
            print(f"PII Location: {match.pii_type.value}")
            print(f"  Text: '{match.text}'")
            print(f"  Line: {match.line_number}")
            print(f"  Position: {match.start_pos}-{match.end_pos}")
            print(f"  Context: '{match.context[:50]}...'")
            
            # Validate line number
            assert 1 <= match.line_number <= len(text_lines), \
                f"Line number should be valid: {match.line_number} (max: {len(text_lines)})"
            
            # Validate character positions
            assert 0 <= match.start_pos < match.end_pos, \
                f"Start position should be before end position: {match.start_pos}-{match.end_pos}"
            
            assert match.end_pos <= len(ocr_result.full_text), \
                f"End position should not exceed text length: {match.end_pos} > {len(ocr_result.full_text)}"
            
            # Validate extracted text matches position
            extracted_text = ocr_result.full_text[match.start_pos:match.end_pos]
            
            # Allow for slight variations in whitespace
            match_text_clean = ''.join(match.text.split())
            extracted_text_clean = ''.join(extracted_text.split())
            
            if match_text_clean not in extracted_text_clean and extracted_text_clean not in match_text_clean:
                print(f"  ⚠ Text mismatch: '{match.text}' vs '{extracted_text}'")
            else:
                print(f"  ✓ Position validation passed")
            
            # Validate context is larger than match
            assert len(match.context) > len(match.text), \
                "Context should be larger than matched text"
        
        if pii_matches:
            print(f"✓ Location tracking validated for {len(pii_matches)} PII matches")
        else:
            print("⚠ No PII matches found for location tracking validation")