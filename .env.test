# Test environment variables for ChromoForge OCR Pipeline

# Google Gemini API Configuration
GOOGLE_API_KEY=test_api_key_for_testing

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://test.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=test_anon_key
SUPABASE_SERVICE_ROLE_KEY=test_service_key

# OCR Processing Configuration
MAX_RETRIES=3
RETRY_DELAY=1.0
BATCH_SIZE=5
MAX_CONCURRENT_REQUESTS=10

# File Processing Configuration
MAX_FILE_SIZE_MB=50
SUPPORTED_FORMATS=pdf
OUTPUT_DIR=./processed
TEMP_DIR=./temp

# PII Detection Configuration
CONFIDENCE_THRESHOLD=0.7
ENABLE_COORDINATE_TRACKING=true
OBFUSCATION_METHOD=black_box

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Enhanced Gemini 2.5 Pro Configuration
GEMINI_MODEL=gemini-2.5-pro
GEMINI_TEMPERATURE=0.1
GEMINI_MAX_TOKENS=8192

# Enhanced Thinking Configuration
GEMINI_THINKING_BUDGET=-1
ENABLE_ULTRA_THINK=true
ENABLE_GOOGLE_SEARCH=false
ENABLE_URL_CONTEXT=false

# Database Transaction Recording
ENABLE_DATABASE_RECORDING=false
DEFAULT_ORGANIZATION_ID=test_org_id

# Enhanced Thai OCR Settings
THAI_CROSS_REFERENCE=true
CONTEXTUAL_NAME_MAPPING=true
MEDICAL_FIELD_EXTRACTION=true