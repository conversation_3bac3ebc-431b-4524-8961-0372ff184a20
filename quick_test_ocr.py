#!/usr/bin/env python3
"""Quick test of OCR functionality without full configuration validation."""

import os
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_gemini_ocr():
    """Test Google Gemini OCR directly."""
    print("🧪 Testing Google Gemini OCR...")
    
    try:
        import google.genai as genai
        
        # Initialize client
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ Google API key not found")
            return False
            
        client = genai.Client(api_key=api_key)
        
        # Find a sample PDF
        sample_dir = Path("original-pdf-examples")
        pdf_files = list(sample_dir.glob("*.pdf"))
        
        if not pdf_files:
            print("❌ No sample PDF files found")
            return False
            
        # Use the first PDF file
        pdf_file = pdf_files[0]
        print(f"📄 Testing with: {pdf_file.name}")
        
        # Read PDF as bytes
        with open(pdf_file, "rb") as f:
            pdf_bytes = f.read()
        
        print(f"📊 PDF size: {len(pdf_bytes):,} bytes")
        
        # Create a simple OCR prompt
        prompt = """
        Please extract all text from this medical document. Focus on:
        1. Patient names
        2. Hospital numbers (HN)
        3. Lab numbers (LN)
        4. Thai national ID numbers
        5. Any medical diagnoses or test results
        
        Return the extracted text in a structured format.
        """
        
        # Test OCR with Gemini
        print("🔄 Processing with Gemini...")
        
        response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents=[
                {
                    "parts": [
                        {"text": prompt},
                        {
                            "inline_data": {
                                "mime_type": "application/pdf",
                                "data": pdf_bytes
                            }
                        }
                    ]
                }
            ]
        )
        
        if response and response.text:
            print("✅ OCR processing successful!")
            print(f"📝 Extracted text length: {len(response.text)} characters")
            print("\n📋 Sample of extracted text:")
            print("-" * 50)
            print(response.text[:500] + "..." if len(response.text) > 500 else response.text)
            print("-" * 50)
            return True
        else:
            print("❌ No text extracted")
            return False
            
    except Exception as e:
        print(f"❌ OCR test failed: {str(e)}")
        return False

async def test_pii_detection():
    """Test PII detection on sample text."""
    print("\n🔍 Testing PII Detection...")
    
    try:
        # Import PII detector
        from src.pii_detector import PIIDetector
        
        # Create detector
        detector = PIIDetector(confidence_threshold=0.7)
        
        # Sample Thai medical text with PII
        sample_text = """
        ชื่อผู้ป่วย: นายสมชาย ใจดี
        เลขบัตรประชาชน: 1-2345-67890-12-3
        HN: 123456
        LN: LAB2024001
        โทรศัพท์: 081-234-5678
        การวินิจฉัย: โรคเบาหวาน
        """
        
        # Detect PII
        pii_matches = detector.detect_pii(sample_text)
        
        print(f"✅ PII Detection successful!")
        print(f"🔍 Found {len(pii_matches)} PII matches:")
        
        for match in pii_matches:
            print(f"  - {match.pii_type.value}: '{match.text}' (confidence: {match.confidence:.2f})")
        
        return len(pii_matches) > 0
        
    except Exception as e:
        print(f"❌ PII detection test failed: {str(e)}")
        return False

async def main():
    """Run quick OCR tests."""
    print("🚀 ChromoForge Quick OCR Test\n")
    
    # Test OCR
    ocr_success = await test_gemini_ocr()
    
    # Test PII Detection
    pii_success = await test_pii_detection()
    
    print(f"\n📊 Test Results:")
    print(f"  OCR Processing: {'✅ PASS' if ocr_success else '❌ FAIL'}")
    print(f"  PII Detection: {'✅ PASS' if pii_success else '❌ FAIL'}")
    
    if ocr_success and pii_success:
        print("\n🎉 Core OCR functionality is working!")
        print("💡 You can now run the full pipeline on your sample files.")
        print("\nNext steps:")
        print("1. Set up Supabase database (optional for basic OCR)")
        print("2. Run: python -m src.main --input original-pdf-examples/CSF\\ TT04035.pdf --output ./test-results")
    else:
        print("\n❌ Some core functionality is not working. Please check the setup.")

if __name__ == "__main__":
    asyncio.run(main())
