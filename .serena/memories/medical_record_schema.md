# Medical Record Schema Definition

## Core Fields for Database Recording

1. **Patient Code**: String starting with 'TT' (e.g., TT04035, TT04040)
2. **Sample Code**: 6-character alphanumeric code (e.g., A1B2C3, XYZ123)
3. **Investigation**: Test name/type (K-TRACK, SPOT-MAS, K4CARE, K-TRACK MET, etc.)
4. **Patient's Full Name (TH)**: Thai language full name
5. **Patient's Full Name (EN)**: English transliteration of name
6. **DOB**: Date of birth in YYYY-MM-DD format (Gregorian calendar)
7. **DOB (BE)**: Date of birth in DD/MM/YYYY format (Buddhist Era, +543 years)
8. **Patient's Contact No.**: Phone number
9. **Place of Treatment**: Healthcare center/hospital name
10. **Referring Physician (TH)**: Doctor's name in Thai
11. **Referring Physician (EN)**: Doctor's name in English
12. **Referring Physician MD Code**: Medical license/registration code
13. **Referring Physician Email**: Array of email addresses

## Key Requirements
- All fields are nullable to handle partial extractions
- Support multiple file patterns and formats
- Generic field names (not specific to any hospital)
- Track which fields need manual completion
- Maintain audit trail for all changes