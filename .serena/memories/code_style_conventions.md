# Code Style and Conventions

## Python Conventions (for OCR Pipeline)
- **Style**: Follow PEP 8 standards
- **Type Hints**: Use type hints for all function parameters and return values
- **Docstrings**: Google-style docstrings for all classes and functions
- **Error Handling**: Comprehensive error handling with logging
- **Async/Await**: Use async patterns for API calls and I/O operations

## Database Conventions
- **Snake Case**: All table and column names in snake_case
- **UUIDs**: Use UUID primary keys for all tables
- **Soft Deletes**: Use deleted_at timestamps instead of hard deletes
- **Audit Logging**: All CRUD operations automatically logged
- **RLS Policies**: Row-Level Security enabled on all tables

## Security Conventions
- **PII Encryption**: All personally identifiable information encrypted at rest
- **Role-Based Access**: Granular permissions based on user roles
- **API Security**: Service role keys never exposed to client
- **Input Validation**: Validate all inputs before processing

## File Organization
- Modular architecture with clear separation of concerns
- Configuration management through environment variables
- Separate modules for OCR, PII detection, and data processing