# Suggested Commands for ChromoForge Development

## Python Development Commands
```bash
# Code formatting
black . --line-length 88
isort .

# Type checking
mypy .

# Linting
flake8 .
pylint src/

# Testing
pytest tests/ -v --cov=src/

# Security scanning
bandit -r src/

# Dependency management
pip install -r requirements.txt
pip freeze > requirements.txt
```

## Database Commands
```bash
# Run database migrations
supabase db push

# Generate TypeScript types
supabase gen types typescript --local > types/database.types.ts

# Reset local database
supabase db reset

# View database logs
supabase logs
```

## File Operations (macOS/Darwin)
```bash
# Find files
find . -name "*.py" -type f

# Search in files
grep -r "pattern" src/

# List directory contents
ls -la

# Change directory
cd /path/to/directory

# View file contents
cat filename.py
head -n 20 filename.py
tail -n 20 filename.py
```

## Development Workflow
```bash
# Start development
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Run OCR pipeline
python src/main.py --input-dir ./original-pdf-examples --output-dir ./processed

# Run tests before commit
pytest && black . && mypy .
```

## Git Commands
```bash
git add .
git commit -m "feat: add OCR processing pipeline"
git push origin main
```