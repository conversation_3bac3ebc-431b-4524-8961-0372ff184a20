# ChromoForge Project Overview

## Purpose
ChromoForge is a medical OCR application designed for processing medical documents (PDFs) containing Thai and English text. The system extracts structured medical data while maintaining HIPAA compliance and data security.

## Tech Stack
- **Database**: Supabase (PostgreSQL with pgcrypto extension)
- **Backend**: Python (planned for OCR pipeline)
- **AI/ML**: Google Gemini 2.5 Pro API for OCR processing
- **Security**: Row-level security (RLS), encrypted PII storage
- **Storage**: Supabase Storage for document management

## Current State
- Database architecture is fully defined with security policies
- Storage buckets and policies configured
- Authentication and role-based access control implemented
- OCR processing pipeline needs to be implemented

## Key Features
- Mixed content OCR (handwritten, Thai, English text)
- PII detection and encryption
- Medical data extraction (patient names, hospital numbers, Thai IDs, lab numbers)
- Audit logging and compliance features
- Role-based access (admin, editor, viewer, analyst)