# Proven Gemini 2.5 Pro Prompt Strategy

## Successful Prompt Template
The following prompt has been proven to achieve 100% accuracy even with difficult-to-read handwritten Thai medical text:

```
Extract only 'Section 1' precisely, focusing on the written and filled-in information; retain each key for every piece of information (key: value). If you are not certain that you can extract or read 100% of the handwritten or inked text accurately, you must look for other printed or stickered information that appears to contain the same or most similar content. Also, use contextual clues to better interpret the text—for example, mapping a person's name to their email address to improve OCR accuracy, especially for Thai characters, or matching the patient's handwritten name in the box with the name on the sticker to better understand the context. ULTRA THINK.
```

## Key Success Factors
1. **ULTRA THINK** directive - Enables deep contextual analysis
2. **Cross-referencing strategy** - Look for printed/stickered alternatives to handwritten text
3. **Contextual mapping** - Use email addresses, stickers, and other clues to verify Thai names
4. **Thinking budget** - Set to -1 for unlimited thinking time
5. **Tools enabled** - URL context and Google Search for additional verification

## Model Configuration
- Model: `gemini-2.5-pro`
- Thinking budget: -1 (unlimited)
- Tools: URL context, Google Search
- Input: Direct PDF processing via base64 encoding

## Extracted Fields Example
- รพ.วชิรพยาบาล ID
- เลขที่บัตร (Thai National ID)
- Full name
- อายุ (Age)
- Date of birth
- Sex
- Phone
- Place of treatment
- Referring physician
- Referring physician's email
- Blood sample collection dates
- Surgery/biopsy dates