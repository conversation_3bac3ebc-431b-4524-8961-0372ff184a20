Metadata-Version: 2.1
Name: supafunc
Version: 0.10.1
Summary: Library for Supabase Functions
Home-page: https://github.com/supabase/functions-py
License: MIT
Author: <PERSON>
Author-email: <EMAIL>
Requires-Python: >=3.9,<4.0
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Dist: httpx[http2] (>=0.26,<0.29)
Requires-Dist: strenum (>=0.4.15,<0.5.0)
Project-URL: Repository, https://github.com/supabase/functions-py
Description-Content-Type: text/markdown

# Functions-py


## Installation

`pip3 install supafunc`

## Usage

Deploy your function as per documentation.


```python3
import asyncio
from supafunc import AsyncFunctionsClient
async def run_func():
    fc = AsyncFunctionsClient("https://<project_ref>.functions.supabase.co", {})
    res = await fc.invoke("payment-sheet", {"responseType": "json"})

if __name__ == "__main__":
    asyncio.run(run_func())
```

