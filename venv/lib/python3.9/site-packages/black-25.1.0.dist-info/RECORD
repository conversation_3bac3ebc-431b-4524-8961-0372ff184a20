../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/_black_version.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/__init__.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/__main__.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/_width_table.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/brackets.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/cache.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/comments.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/concurrency.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/const.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/debug.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/files.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/handle_ipynb_magics.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/linegen.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/lines.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/mode.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/nodes.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/numerics.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/output.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/parsing.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/ranges.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/report.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/resources/__init__.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/rusty.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/schema.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/strings.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/black/trans.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blackd/__init__.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blackd/__main__.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blackd/middlewares.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/__init__.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/pgen2/__init__.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/pgen2/conv.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/pgen2/driver.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/pgen2/grammar.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/pgen2/literals.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/pgen2/parse.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/pgen2/pgen.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/pgen2/token.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/pgen2/tokenize.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/pygram.cpython-39.pyc,,
../../../../../../Library/Caches/com.apple.python/Users/<USER>/Desktop/ChromoForge/venv/lib/python3.9/site-packages/blib2to3/pytree.cpython-39.pyc,,
../../../bin/black,sha256=FmCfQphx-fbthUdaBZxPNhfF9Q6XuaCqGJmYVuKPcrs,235
../../../bin/blackd,sha256=thA41C3gsrFE0Vj32dYBtMU7ta8YDIIh-_-EC4sSnkQ,236
30fcd23745efe32ce681__mypyc.cpython-39-darwin.so,sha256=G43EFoJ21wHiw5Bxb8dOwjwVKkaDjegis6x3aBsTdS8,4057088
_black_version.py,sha256=FhAjEtEWGP4vzVPv6krA2rMrSoPtKcVJ74gjENa73Zo,19
black-25.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
black-25.1.0.dist-info/METADATA,sha256=oSdftyY9ijULKJlSA4hI4IOzomCceuRzrWPylASmVac,81269
black-25.1.0.dist-info/RECORD,,
black-25.1.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black-25.1.0.dist-info/WHEEL,sha256=nxx7jdlNHRJTBMTXZxW23yLJgdzzR01LJCi6lxCXMQQ,104
black-25.1.0.dist-info/entry_points.txt,sha256=XTCA4X2yVA0tMiV7l96Gv9TyxhVhoCaznLN2XThqYSA,144
black-25.1.0.dist-info/licenses/AUTHORS.md,sha256=q4LhA36Sf7X6e5xzVq7dFClyVKdxK2z7gpos_YsGrIg,8149
black-25.1.0.dist-info/licenses/LICENSE,sha256=nAQo8MO0d5hQz1vZbhGqqK_HLUqG1KNiI9erouWNbgA,1080
black/__init__.cpython-39-darwin.so,sha256=H_LgnDKA8QSbORCY4K3J-FWlGEcW1sWdcUk8TFvPvMM,50144
black/__init__.py,sha256=M4-GQzsO9_OdhW0EofQrkW3n5Z1xUQ__kqjUfiAO15Y,51644
black/__main__.py,sha256=mogeA4o9zt4w-ufKvaQjSEhtSgQkcMVLK9ChvdB5wH8,47
black/_width_table.cpython-39-darwin.so,sha256=u7Sr2_kKPntdSLA2ypCs3CbGSHRRnteCnDnaBk8IDbc,50160
black/_width_table.py,sha256=3qJd3E9YehKhRowZzBOfTv5Uv9gnFX-cAi4ydwxu0q0,10748
black/brackets.cpython-39-darwin.so,sha256=FeLh5qgVJr9bcxUw1IkNgc8wBRHZB5IcPj-AiN78onw,50144
black/brackets.py,sha256=nSMRUC9-WxZ6xIAlCGkvl5MQYbldJj4fQiGzi-QMSq4,12429
black/cache.cpython-39-darwin.so,sha256=pEHSFTLS2nzlU9xE_Kdm259NvGJSJKTsIXQBziUAZ30,50136
black/cache.py,sha256=_N51IHzj0-D55kDuk8v9hm0TfKfsJv1bQL3anxUR4k4,4754
black/comments.cpython-39-darwin.so,sha256=2PV26rpUE-F31RXtJJ_bOgfYLQZR6do2gwEQTyFNTyY,50144
black/comments.py,sha256=Bi72oBehZOVkyoo_WSTO0hnRFRP2--LmpUstmIlux6o,15818
black/concurrency.py,sha256=nsQKuu_ZMeaWi-k3E-HTqjTDlwLV6k92vOaJgjskWqw,6432
black/const.cpython-39-darwin.so,sha256=LrAFPvHWP1ETrMlCHaY01zgv8jxfytsHAPzd_iYk1U4,50136
black/const.py,sha256=U7cDnhWljmrieOtPBUdO2Vcz69J_VXB6-Br94wuCVuo,321
black/debug.py,sha256=yJBVRbD-jYgPK-tKksgcHUgmjqU9ggBfyve5hQSLlEg,1927
black/files.py,sha256=xaBMK-pvfqORqMFik-CC7Eaeq_51xyyhJ3_ENWBrdiY,14722
black/handle_ipynb_magics.cpython-39-darwin.so,sha256=4qyeso_Aq-AHFih9ma_uSdeR2ODSYLCsDZcy-vkM3fY,50184
black/handle_ipynb_magics.py,sha256=1s9RD59lOgS71fOgE8bEXJiPeUdEouUYPTu6_wv5f6c,15494
black/linegen.cpython-39-darwin.so,sha256=rYrpaWjbeZXdNAZJkI5C_y4ZWFIKN40bS6WoGi6Tai8,50144
black/linegen.py,sha256=TdJ7AVf7YyqERXZOkNZJ1cllGUQ0nxDI2iRZUcZJpgM,70488
black/lines.cpython-39-darwin.so,sha256=F07bwRkU2JoGXbVdA-s0gz0Oze-FD4m84Is-FT9CwcQ,50136
black/lines.py,sha256=hC1td-dENO_4QoTNY78GP8DME2cZh1i_O-BrN-BL2ho,39620
black/mode.cpython-39-darwin.so,sha256=E_o73oJ7IhxdRbXCak8RdluvFEZABFF_knsvPs40as8,50136
black/mode.py,sha256=y1_iRcvfCVxmSvFbnNsMTUXXocBZrLaOUZq_w8SqBW0,9065
black/nodes.cpython-39-darwin.so,sha256=ERFRgtjDvDZ8wG8ZKZza4_yx9BwHoHXZDHGYG8byXP0,50136
black/nodes.py,sha256=XFNkJEyZFMZq0R0bPn_cT7fEhy6vSu2qoZGELChUMS4,30418
black/numerics.cpython-39-darwin.so,sha256=QaE9OzMN97xxyf2p9c6tD9gL6PJ7Ol35QL5PePyK3Xg,50144
black/numerics.py,sha256=xRGnTSdMVbTaA9IGechc8JM-cIuJGCc326s71hkXJIw,1655
black/output.py,sha256=z8vs-bWADomxHav4sy_YpX_QpauGFp5SXr3Gj7kQDKI,3933
black/parsing.cpython-39-darwin.so,sha256=-VjZxyPAUe0OM4iVAe73HRMvbTQMSjswvFAH5zv_fkY,50144
black/parsing.py,sha256=eyf1PGJZ6MKV7lky37m-RmTLxUL2ggcvffqjxi0meRA,8621
black/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black/ranges.cpython-39-darwin.so,sha256=G6Q1RVqN1r1k_iWDubPA5HHd51WqtxUZmwgNLw9L_-Y,50136
black/ranges.py,sha256=aegh-sCgti-okdOWd-0o9UZFyh5BMoBuxg-n80MehNc,19704
black/report.py,sha256=igkNi8iR5FqSa1sXddS-HnoqW7Cq7PveCmFCfd-pN6w,3452
black/resources/__init__.cpython-39-darwin.so,sha256=1mVhDIQPcRpGJXO6oHHdWgHM7ASuIzcedyxAExSU-gA,50160
black/resources/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black/resources/black.schema.json,sha256=PL61UkUw7DSl2OrC6BqBM1wXu5O6IzUSUdDdU_QZWGM,7137
black/rusty.cpython-39-darwin.so,sha256=MXGzvZCfn2TTq8ytAat5dJEIi3ZjpwbMe6z1ZULzjI4,50136
black/rusty.py,sha256=4LKo3KTUWYZ2cN6QKmwwZVbsCNt2fpu5lzue2V-uxIA,557
black/schema.cpython-39-darwin.so,sha256=dNizAwLQPy6U3VK5Du3oUxXIFzjrjP7qrevCu4TjqGE,50136
black/schema.py,sha256=ru0z9EA-f3wfLLVPefeFpM0s4GYsYg_UjpOiMLhjdbA,431
black/strings.cpython-39-darwin.so,sha256=fwtECgDn82xNb8asJ4ZJKMGnqP_K15pVz70F3JdDMec,50144
black/strings.py,sha256=VQ04cvueKygPkmNFeumBvxdJX2GE-XlYby7bXGoPthI,13220
black/trans.cpython-39-darwin.so,sha256=sGpHKQGBUa8DgFRqJvfRELcjS0mBK0aNl0A7Atxkw6c,50136
black/trans.py,sha256=xrb16nZMFB9SstT4kCE6HQN_mOOEQh3IcKL_iS3Jj14,95191
blackd/__init__.py,sha256=V9-BiApAg1drY7fajHoAYAVPthmA7BzZNLhmTjLJ0Kc,8879
blackd/__main__.py,sha256=L4xAcDh1K5zb6SsJB102AewW2G13P9-w2RiEwuFj8WA,37
blackd/middlewares.py,sha256=kZZavG9kvAbXKrlwIQCnDqK6fZ9FyAYzdKwqp_IcHpg,1172
blib2to3/Grammar.txt,sha256=zjM1rSC9GJjnboYyRDZyKx2IPWDkscVdodwQpDCs4So,11700
blib2to3/LICENSE,sha256=V4mIG4rrnJH1g19bt8q-hKD-zUuyvi9UyeaVenjseZ0,12762
blib2to3/PatternGrammar.txt,sha256=7lul2ztnIqDi--JWDrwciD5yMo75w7TaHHxdHMZJvOM,793
blib2to3/README,sha256=QYZYIfb1NXTTYqDV4kn8oRcNG_qlTFYH1sr3V1h65ko,1074
blib2to3/__init__.py,sha256=9_8wL9Scv8_Cs8HJyJHGvx1vwXErsuvlsAqNZLcJQR0,8
blib2to3/pgen2/__init__.py,sha256=hY6w9QUzvTvRb-MoFfd_q_7ZLt6IUHC2yxWCfsZupQA,143
blib2to3/pgen2/conv.cpython-39-darwin.so,sha256=L8P-nX3py4wC_3D1EnEwcvsC0Msc9K0SM7qKDWntQhM,50136
blib2to3/pgen2/conv.py,sha256=vH8a_gkalWRNxuNPRxkoigw8_UobdHHSw-PyUcUuH8I,9587
blib2to3/pgen2/driver.cpython-39-darwin.so,sha256=LE0zQAu5SSzi7mGfOvGbcX3S73YKB0sDBBAQTs-rf-o,50136
blib2to3/pgen2/driver.py,sha256=zoEqEI_Z0SYlJqphc2E7CFvHwrlSNv9yscATAJ6M87c,10846
blib2to3/pgen2/grammar.cpython-39-darwin.so,sha256=nHL4MakJ7QdbJT7PXYOwvu6OVMNLmptiZq7cKgOv7Nw,50144
blib2to3/pgen2/grammar.py,sha256=xApSZeigr9IBog2G9_vLvhOiKqUjZrQOPHlrieCw8lE,6846
blib2to3/pgen2/literals.cpython-39-darwin.so,sha256=nZrNRULnMHEx0gqqadbhNxo01qn_7IhKmkp88uMuEJ4,50144
blib2to3/pgen2/literals.py,sha256=i-Y8SlaJ7dU6jntWQm_g8GpG2zl1akZmZhsz1igdYR8,1586
blib2to3/pgen2/parse.cpython-39-darwin.so,sha256=HAcLxPWn4J2_gExzFp_bOb13wv04rznbtxNnzjARoLM,50136
blib2to3/pgen2/parse.py,sha256=ILEYny98jrfODxMG3MADPBaWuaDu3wpfYLp5rrdV_jY,15612
blib2to3/pgen2/pgen.cpython-39-darwin.so,sha256=LZwGTHFB1DpWny-PpQeFcQYYc_YEeJC6pymghYEfxKc,50136
blib2to3/pgen2/pgen.py,sha256=TT5etH65ltNwcJakhK50u3Z8ZiOo7Bj2CjbCnM7AiyU,15418
blib2to3/pgen2/token.cpython-39-darwin.so,sha256=K2pUo1UNUkhmWh_YIrHR-o__bSiaJk3WtQZLzxJu9yM,50136
blib2to3/pgen2/token.py,sha256=pb5cvttERocFGRWjJ5C1f_a0S4C_UKmTfHXMqyFKoig,1893
blib2to3/pgen2/tokenize.cpython-39-darwin.so,sha256=710vwLZ8Fo-sz1NPC83_hW4hiMRUFXPqmXolT-UR9q0,50144
blib2to3/pgen2/tokenize.py,sha256=dc6fou2882mdBLHm6Ik-qsTkzux7T-iQAftPZs0V-2Q,41468
blib2to3/pygram.cpython-39-darwin.so,sha256=v5rDRb0rqFa9H0MCf4ULz0ljIRahXUrjRSG_b2bN9_Q,50136
blib2to3/pygram.py,sha256=l2qw7mw8I533KGWAXUFCXPGCN5F66hvYg86g-EA9GEg,4915
blib2to3/pytree.cpython-39-darwin.so,sha256=iVIN5UeGmAIiGvErk22rSYmGmAm1f_v_dR3bCVr51OI,50136
blib2to3/pytree.py,sha256=52hBl0unVlUdec-LojHpey6j98Qcrrd_HXQSxTj2StY,32624
