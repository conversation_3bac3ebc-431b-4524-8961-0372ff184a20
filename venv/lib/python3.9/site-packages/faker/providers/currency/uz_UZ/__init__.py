from .. import Provider as CurrencyProvider


class Provider(CurrencyProvider):
    # Format: (code, name)
    currencies = (
        ("AED", "BAA Dirhami"),
        ("AFN", "Afg‘oni"),
        ("ALL", "Lek"),
        ("AMD", "Arman dramasi"),
        ("ANG", "Niderlandiya Antil guldeni"),
        ("AOA", "Kvanza"),
        ("ARS", "Argentina pesosi"),
        ("AUD", "Avstraliya dollari"),
        ("AWG", "Aruba florini"),
        ("AZN", "Ozarbayjon manati"),
        ("BAM", "Bosniya va Gertsegovina konvertatsiya qilinadigan markasi"),
        ("BBD", "Barbados dollari"),
        ("BDT", "Taka"),
        ("BGN", "Bolgariya levi"),
        ("BHD", "Bahrayn dinori"),
        ("BIF", "Burundi franki"),
        ("BMD", "Bermuda dollari"),
        ("BND", "<PERSON>runey dollari"),
        ("BOB", "Boliviano"),
        ("BRL", "Braziliya reali"),
        ("BSD", "Bagama dollari"),
        ("BTN", "Ngultrum"),
        ("BWP", "Pula"),
        ("BYR", "Belarus rubli"),
        ("BZD", "Beliz dollari"),
        ("CAD", "Kanada dollari"),
        ("CDF", "Kongo franki"),
        ("CHF", "Shveytsariya franki"),
        ("CLP", "Chili pesosi"),
        ("CNY", "Yuan"),
        ("COP", "Kolumbiya pesosi"),
        ("CRC", "Kosta-Rika koloni"),
        ("CUC", "Kuba konvertatsiya qilinadigan pesosi"),
        ("CUP", "Kuba pesosi"),
        ("CVE", "Kabo-Verde eskudosi"),
        ("CZK", "Chex kronasi"),
        ("DJF", "Jibuti franki"),
        ("DKK", "Daniya kronasi"),
        ("DOP", "Dominikan pesosi"),
        ("DZD", "Jazoir dinori"),
        ("EGP", "Misr funti"),
        ("ERN", "Nakfa"),
        ("ETB", "Efiopiya biri"),
        ("EUR", "Yevro"),
        ("FJD", "Fiji dollari"),
        ("FKP", "Folklend orollari funti"),
        ("GBP", "Funt sterling"),
        ("GEL", "Lari"),
        ("GGP", "Gernsi funti"),
        ("GHS", "Gana sedi"),
        ("GIP", "Gibraltar funti"),
        ("GMD", "Dalasi"),
        ("GNF", "Gvineya franki"),
        ("GTQ", "Ketsal"),
        ("GYD", "Gayana dollari"),
        ("HKD", "Gonkong dollari"),
        ("HNL", "Lempira"),
        ("HRK", "Xorvatiya kunasi"),
        ("HTG", "Gurda"),
        ("HUF", "Forint"),
        ("IDR", "Indoneziya rupiyasi"),
        ("ILS", "Yangi Isroil shekeli"),
        ("NIS", "Yangi Isroil shekeli"),
        ("IMP", "Men oroli funti"),
        ("INR", "Hind rupiyasi"),
        ("IQD", "Iroq dinori"),
        ("IRR", "Eron riali"),
        ("ISK", "Islandiya kronasi"),
        ("JEP", "Jersi funti"),
        ("JMD", "Yamayka dollari"),
        ("JOD", "Iordaniya dinori"),
        ("JPY", "Yena"),
        ("KES", "Keniya shillingi"),
        ("KGS", "Som"),
        ("KHR", "Riyel"),
        ("KMF", "Komor franki"),
        ("KPW", "Shimoliy Koreya voni"),
        ("KRW", "Janubiy Koreya voni"),
        ("KWD", "Kuvayt dinori"),
        ("KYD", "Kayman orollari dollari"),
        ("KZT", "Tenge"),
        ("LAK", "Kip"),
        ("LBP", "Livan funti"),
        ("LKR", "Shri-Lanka rupiyasi"),
        ("LRD", "Liberiya dollari"),
        ("LSL", "Loti"),
        ("LTL", "Litva liti"),
        ("LYD", "Liviya dinori"),
        ("MAD", "Marokash dirhami"),
        ("MDL", "Moldaviya leyi"),
        ("MGA", "Malagasi ariari"),
        ("MKD", "Denar"),
        ("MMK", "Kyat"),
        ("MNT", "Tugrik"),
        ("MOP", "Pataka"),
        ("MRO", "Ugiyya"),
        ("MUR", "Mavrikiy rupiyasi"),
        ("MVR", "Rufiya"),
        ("MWK", "Kvacha"),
        ("MXN", "Meksika pesosi"),
        ("MYR", "Malayziya ringgiti"),
        ("MZN", "Mozambik metikali"),
        ("NAD", "Namibiya dollari"),
        ("NGN", "Nayra"),
        ("NIO", "Kordoba"),
        ("NOK", "Norvegiya kronasi"),
        ("NPR", "Nepal rupiyasi"),
        ("NZD", "Yangi Zelandiya dollari"),
        ("OMR", "Ummon riali"),
        ("PAB", "Balboa"),
        ("PEN", "Sol"),
        ("PGK", "Kina"),
        ("PHP", "Filippin pesosi"),
        ("PKR", "Pokiston rupiyasi"),
        ("PLN", "Zlotiy"),
        ("PYG", "Guarani"),
        ("QAR", "Qatar riali"),
        ("RON", "Ruminiya leyi"),
        ("RSD", "Serbiya dinori"),
        ("RUB", "Rossiya rubli"),
        ("RWF", "Ruanda franki"),
        ("SAR", "Saudiya riyoli"),
        ("SBD", "Solomon orollari dollari"),
        ("SCR", "Seyshel rupiyasi"),
        ("SDG", "Sudan funti"),
        ("SEK", "Shvetsiya kronasi"),
        ("SGD", "Singapur dollari"),
        ("SHP", "Muqaddas Yelena funti"),
        ("SLL", "Leone"),
        ("SOS", "Somali shillingi"),
        ("SPL", "Luigino"),
        ("SRD", "Surinam dollari"),
        ("STD", "Dobra"),
        ("SVC", "Salvador koloni"),
        ("SYP", "Suriya funti"),
        ("SZL", "Lilangeni"),
        ("THB", "Bat"),
        ("TJS", "Somoniy"),
        ("TMT", "Yangi Turkman manati"),
        ("TND", "Tunis dinori"),
        ("TOP", "Paanga"),
        ("TRY", "Turk lirasi"),
        ("TTD", "Trinidad va Tobago dollari"),
        ("TVD", "Tuvalu dollari"),
        ("TWD", "Yangi Tayvan dollari"),
        ("TZS", "Tanzaniya shillingi"),
        ("UAH", "Grivna"),
        ("UGX", "Uganda shillingi"),
        ("USD", "AQSh dollari"),
        ("UYU", "Urugvay pesosi"),
        ("UZS", "Oʻzbek so‘mi"),
        ("VEF", "Suveren bolivar"),
        ("VND", "Dong"),
        ("VUV", "Vatu"),
        ("WST", "Tala"),
        ("XAF", "KFA franki BEAS"),
        ("XCD", "Sharqiy Karib dollari"),
        ("XDR", "SDR"),
        ("XOF", "KFA franki BCEAO"),
        ("XPF", "KFP franki"),
        ("YER", "Yaman riali"),
        ("ZAR", "Rand"),
        ("ZMW", "Zambiya kvachasi"),
        ("ZWD", "Zimbabve dollari"),
    )

    price_formats = ["#,##", "%#,##", "%##,##", "% ###,##", "%# ###,##"]

    def pricetag(self) -> str:
        return (
            self.numerify(self.random_element(self.price_formats)) + "\N{NO-BREAK SPACE}\N{CYRILLIC SMALL LETTER ER}."
        )
