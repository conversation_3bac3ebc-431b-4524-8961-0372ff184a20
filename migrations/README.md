# ChromoForge OCR Pipeline Database Migrations

This directory contains SQL migration files for extending the ChromoForge Supabase architecture with comprehensive OCR processing capabilities.

## Migration Files

### 001_ocr_pipeline_tables.sql
**Purpose**: Creates core tables for OCR processing pipeline

**Tables Created**:
- `ocr_processing_transactions` - Tracks individual OCR processing attempts with detailed metadata
- `batch_processing_jobs` - Manages batch OCR processing operations
- `medical_records_extraction` - Stores detailed field-level extraction data with versioning
- `field_extraction_audit` - Tracks changes to individual fields for HIPAA compliance

**Key Features**:
- Field-level confidence scores and metadata
- Version control for extracted data
- Comprehensive error tracking and retry logic
- PII encryption for sensitive fields
- Full audit trail support

### 002_ocr_pipeline_secure_views.sql
**Purpose**: Creates secure views with role-based PII access control

**Views Created**:
- `medical_extractions_secure` - Main view with conditional PII decryption
- `extraction_quality_analytics` - Aggregated quality metrics
- `ocr_processing_performance` - Processing performance analytics
- `active_batch_jobs` - Real-time batch job monitoring
- `field_change_history` - Audit trail for field modifications

**Security Features**:
- Role-based PII decryption (editors/admins only)
- Redacted values for viewers/analysts
- Organization-scoped data access
- Audit trail visibility controls

### 003_ocr_pipeline_indexes_optimization.sql
**Purpose**: Optimizes database performance with specialized indexes

**Optimizations**:
- Composite indexes for common query patterns
- Partial indexes for specific workflows
- Materialized views for analytics
- Performance monitoring capabilities
- Automated maintenance functions

**Performance Features**:
- Query performance tracking
- Index usage monitoring
- Automated statistics refresh
- Slow query identification

## Installation Instructions

1. **Prerequisites**:
   - Existing ChromoForge Supabase setup (supabase-setup.sql)
   - Supabase project with required extensions enabled
   - Admin access to execute migrations

2. **Execute migrations in order**:
   ```sql
   -- Run in Supabase SQL Editor
   -- Execute each file in sequence:
   -- 1. 001_ocr_pipeline_tables.sql
   -- 2. 002_ocr_pipeline_secure_views.sql
   -- 3. 003_ocr_pipeline_indexes_optimization.sql
   ```

3. **Post-installation setup**:
   ```sql
   -- Create initial encryption key for your organization
   SELECT create_test_encryption_key(
     (SELECT id FROM organizations WHERE name = 'Your Organization Name')
   );
   
   -- Refresh materialized views
   SELECT refresh_ocr_statistics();
   
   -- Set up regular maintenance (recommended daily)
   SELECT maintain_ocr_tables();
   ```

## Key Features

### 1. Multi-Tenancy Support
- All tables include `organization_id` for data isolation
- Row-level security policies enforce organization boundaries
- Cross-organization queries prevented by default

### 2. HIPAA Compliance
- PII fields encrypted at rest using organization-specific keys
- Comprehensive audit logging for all data access
- Field-level change tracking with user attribution
- Role-based access control for sensitive data

### 3. Version Control
- Extracted data versioning with parent-child relationships
- Ability to track changes over time
- Rollback capabilities for corrections

### 4. Batch Processing
- Async batch job management
- Progress tracking and estimation
- Priority-based processing queues
- Automatic retry with exponential backoff

### 5. Field-Level Metadata
- Individual confidence scores per extracted field
- Extraction metadata (warnings, cross-references)
- Manual review flags and tracking
- Quality assurance workflows

## Data Model Overview

### Core Relationships
```
documents (existing)
    ↓
ocr_processing_transactions
    ↓
medical_records_extraction (versioned)
    ↓
field_extraction_audit
```

### Processing Flow
1. Document uploaded → creates `documents` record
2. OCR processing initiated → creates `ocr_processing_transactions`
3. Successful extraction → creates `medical_records_extraction`
4. Manual corrections → new version with audit trail

## Security Considerations

### PII Encryption
- Patient names, IDs, and contact info encrypted using `pgp_sym_encrypt`
- Organization-specific encryption keys
- Automatic decryption in secure views based on user role

### Access Control
- Viewers: Can see aggregated data, no PII
- Editors: Full PII access, can create/update records
- Admins: Full access including audit trails
- Analysts: Statistical access, no PII

## Performance Optimization

### Index Strategy
- Composite indexes for common filter combinations
- Partial indexes for specific workflows
- GIN indexes for full-text search
- BRIN indexes for time-series data

### Materialized Views
- Daily extraction statistics
- Hourly processing performance
- Automatic refresh via `refresh_ocr_statistics()`

## Monitoring and Maintenance

### Performance Monitoring
```sql
-- View current performance metrics
SELECT * FROM ocr_performance_dashboard;

-- Analyze index usage
SELECT * FROM analyze_index_usage();

-- Identify slow queries
SELECT * FROM identify_slow_ocr_queries();
```

### Regular Maintenance
```sql
-- Run daily
SELECT maintain_ocr_tables();
SELECT refresh_ocr_statistics();

-- Run monthly
SELECT archive_old_ocr_logs(90);
```

## Troubleshooting

### Common Issues

1. **Slow extraction queries**:
   - Check index usage with `analyze_index_usage()`
   - Ensure materialized views are refreshed
   - Run `VACUUM ANALYZE` on main tables

2. **High storage usage**:
   - Archive old logs with `archive_old_ocr_logs()`
   - Check for unused indexes
   - Review text storage in `extracted_text_full`

3. **Failed OCR processing**:
   - Check `ocr_processing_transactions` for error patterns
   - Review retry settings and thresholds
   - Monitor API quota usage

## Future Enhancements

Potential improvements for future migrations:
- Partitioning for large-scale deployments
- Advanced ML confidence scoring
- Multi-language extraction support
- Integration with external validation services
- Real-time processing notifications