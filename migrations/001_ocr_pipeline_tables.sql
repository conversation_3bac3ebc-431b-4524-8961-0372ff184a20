-- ChromoForge OCR Pipeline Tables Migration
-- Version: 001
-- Description: Adds comprehensive OCR processing tables with field-level extraction support
-- Author: System Architect
-- Date: 2025-01-04

-- =====================================================
-- STEP 1: Create Additional Enums for OCR Processing
-- =====================================================

-- Processing status for OCR jobs
CREATE TYPE ocr_processing_status AS ENUM (
    'queued',
    'processing',
    'completed',
    'failed',
    'cancelled',
    'retry_pending'
);

-- Batch processing status
CREATE TYPE batch_status AS ENUM (
    'pending',
    'running',
    'completed',
    'failed',
    'partially_completed',
    'cancelled'
);

-- Field extraction confidence levels
CREATE TYPE confidence_level AS ENUM (
    'high',      -- > 0.9
    'medium',    -- 0.7 - 0.9
    'low',       -- 0.5 - 0.7
    'very_low'   -- < 0.5
);

-- =====================================================
-- STEP 2: Create OCR Processing Tables
-- =====================================================

-- OCR Processing Transactions Table
-- Tracks individual OCR processing attempts with detailed metadata
CREATE TABLE ocr_processing_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    document_id UUID NOT NULL REFERENCES documents(id),
    batch_id UUID, -- References batch_processing_jobs if part of batch
    
    -- Processing status
    status ocr_processing_status NOT NULL DEFAULT 'queued',
    attempt_number INTEGER NOT NULL DEFAULT 1,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    
    -- Processing metadata
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    processing_duration_ms INTEGER,
    
    -- Model information
    model_name TEXT NOT NULL DEFAULT 'gemini-2.5-pro',
    model_version TEXT,
    model_parameters JSONB DEFAULT '{}',
    
    -- Resource usage
    tokens_used INTEGER,
    api_cost_usd DECIMAL(10,4),
    
    -- Error tracking
    error_code TEXT,
    error_message TEXT,
    error_details JSONB,
    
    -- Retry information
    retry_after TIMESTAMPTZ,
    retry_reason TEXT,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),
    
    CONSTRAINT ocr_processing_positive_duration CHECK (processing_duration_ms IS NULL OR processing_duration_ms >= 0),
    CONSTRAINT ocr_processing_positive_attempts CHECK (attempt_number > 0 AND max_attempts > 0)
);

-- Indexes for OCR processing transactions
CREATE INDEX idx_ocr_processing_organization_id ON ocr_processing_transactions(organization_id);
CREATE INDEX idx_ocr_processing_document_id ON ocr_processing_transactions(document_id);
CREATE INDEX idx_ocr_processing_batch_id ON ocr_processing_transactions(batch_id) WHERE batch_id IS NOT NULL;
CREATE INDEX idx_ocr_processing_status ON ocr_processing_transactions(status);
CREATE INDEX idx_ocr_processing_created_at ON ocr_processing_transactions(created_at DESC);
CREATE INDEX idx_ocr_processing_retry_after ON ocr_processing_transactions(retry_after) WHERE retry_after IS NOT NULL;

-- Batch Processing Jobs Table
-- Manages batch OCR processing operations
CREATE TABLE batch_processing_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    
    -- Batch metadata
    name TEXT NOT NULL,
    description TEXT,
    
    -- Processing status
    status batch_status NOT NULL DEFAULT 'pending',
    total_documents INTEGER NOT NULL DEFAULT 0,
    processed_documents INTEGER NOT NULL DEFAULT 0,
    successful_documents INTEGER NOT NULL DEFAULT 0,
    failed_documents INTEGER NOT NULL DEFAULT 0,
    
    -- Timing
    scheduled_at TIMESTAMPTZ,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    estimated_completion_at TIMESTAMPTZ,
    
    -- Configuration
    processing_config JSONB DEFAULT '{}',
    priority INTEGER NOT NULL DEFAULT 5, -- 1-10, higher is more important
    parallel_workers INTEGER NOT NULL DEFAULT 1,
    
    -- Error tracking
    last_error TEXT,
    error_count INTEGER NOT NULL DEFAULT 0,
    
    -- Progress tracking
    progress_percentage DECIMAL(5,2) DEFAULT 0.0,
    current_document_id UUID,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES user_profiles(id),
    cancelled_by UUID REFERENCES user_profiles(id),
    cancelled_at TIMESTAMPTZ,
    
    CONSTRAINT batch_processing_document_counts CHECK (
        processed_documents >= 0 AND
        successful_documents >= 0 AND
        failed_documents >= 0 AND
        processed_documents = successful_documents + failed_documents
    ),
    CONSTRAINT batch_processing_priority_range CHECK (priority >= 1 AND priority <= 10),
    CONSTRAINT batch_processing_progress_range CHECK (progress_percentage >= 0 AND progress_percentage <= 100)
);

-- Indexes for batch processing
CREATE INDEX idx_batch_processing_organization_id ON batch_processing_jobs(organization_id);
CREATE INDEX idx_batch_processing_status ON batch_processing_jobs(status);
CREATE INDEX idx_batch_processing_priority ON batch_processing_jobs(priority DESC);
CREATE INDEX idx_batch_processing_created_at ON batch_processing_jobs(created_at DESC);

-- Medical Records Extraction Table
-- Stores detailed field-level extraction data with versioning
CREATE TABLE medical_records_extraction (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES documents(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    transaction_id UUID NOT NULL REFERENCES ocr_processing_transactions(id),
    
    -- Version control
    version INTEGER NOT NULL DEFAULT 1,
    is_current BOOLEAN NOT NULL DEFAULT true,
    parent_version_id UUID REFERENCES medical_records_extraction(id),
    
    -- Extracted patient information (encrypted)
    patient_name_encrypted BYTEA,
    patient_name_confidence DECIMAL(3,2),
    patient_name_metadata JSONB DEFAULT '{}',
    
    thai_id_encrypted BYTEA,
    thai_id_confidence DECIMAL(3,2),
    thai_id_metadata JSONB DEFAULT '{}',
    
    -- Hospital identifiers
    hospital_id TEXT,
    hospital_id_confidence DECIMAL(3,2),
    hospital_id_metadata JSONB DEFAULT '{}',
    
    hospital_number_encrypted BYTEA,
    hospital_number_confidence DECIMAL(3,2),
    hospital_number_metadata JSONB DEFAULT '{}',
    
    lab_number_encrypted BYTEA,
    lab_number_confidence DECIMAL(3,2),
    lab_number_metadata JSONB DEFAULT '{}',
    
    -- Patient demographics
    age TEXT,
    age_confidence DECIMAL(3,2),
    
    date_of_birth_encrypted BYTEA,
    date_of_birth_confidence DECIMAL(3,2),
    
    sex TEXT,
    sex_confidence DECIMAL(3,2),
    
    phone_encrypted BYTEA,
    phone_confidence DECIMAL(3,2),
    
    -- Medical facility information
    place_of_treatment TEXT,
    place_of_treatment_confidence DECIMAL(3,2),
    
    -- Referring physician information
    referring_physician_encrypted BYTEA,
    referring_physician_confidence DECIMAL(3,2),
    
    referring_physician_email_encrypted BYTEA,
    referring_physician_email_confidence DECIMAL(3,2),
    
    -- Date arrays stored as JSONB
    blood_collection_dates JSONB DEFAULT '[]',
    blood_collection_dates_confidence DECIMAL(3,2),
    
    surgery_biopsy_dates JSONB DEFAULT '[]',
    surgery_biopsy_dates_confidence DECIMAL(3,2),
    
    -- Medical information as JSONB
    diagnoses JSONB DEFAULT '[]',
    diagnoses_confidence DECIMAL(3,2),
    
    medications JSONB DEFAULT '[]',
    medications_confidence DECIMAL(3,2),
    
    test_results JSONB DEFAULT '{}',
    test_results_confidence DECIMAL(3,2),
    
    -- Overall extraction quality
    overall_confidence_score DECIMAL(3,2) NOT NULL,
    confidence_level confidence_level GENERATED ALWAYS AS (
        CASE 
            WHEN overall_confidence_score > 0.9 THEN 'high'::confidence_level
            WHEN overall_confidence_score > 0.7 THEN 'medium'::confidence_level
            WHEN overall_confidence_score > 0.5 THEN 'low'::confidence_level
            ELSE 'very_low'::confidence_level
        END
    ) STORED,
    
    -- Extraction metadata
    extracted_text_full TEXT,
    detected_languages TEXT[],
    extraction_warnings JSONB DEFAULT '[]',
    cross_reference_notes JSONB DEFAULT '[]',
    contextual_clues_used JSONB DEFAULT '[]',
    
    -- Quality assurance
    manual_review_required BOOLEAN DEFAULT false,
    reviewed_by UUID REFERENCES user_profiles(id),
    reviewed_at TIMESTAMPTZ,
    review_notes TEXT,
    
    -- Search vector for full-text search
    search_vector tsvector,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT extraction_confidence_range CHECK (
        overall_confidence_score >= 0 AND overall_confidence_score <= 1
    ),
    CONSTRAINT extraction_field_confidence_range CHECK (
        (patient_name_confidence IS NULL OR (patient_name_confidence >= 0 AND patient_name_confidence <= 1)) AND
        (thai_id_confidence IS NULL OR (thai_id_confidence >= 0 AND thai_id_confidence <= 1)) AND
        (hospital_id_confidence IS NULL OR (hospital_id_confidence >= 0 AND hospital_id_confidence <= 1)) AND
        (hospital_number_confidence IS NULL OR (hospital_number_confidence >= 0 AND hospital_number_confidence <= 1)) AND
        (lab_number_confidence IS NULL OR (lab_number_confidence >= 0 AND lab_number_confidence <= 1))
    )
);

-- Indexes for medical records extraction
CREATE INDEX idx_extraction_document_id ON medical_records_extraction(document_id);
CREATE INDEX idx_extraction_organization_id ON medical_records_extraction(organization_id);
CREATE INDEX idx_extraction_transaction_id ON medical_records_extraction(transaction_id);
CREATE INDEX idx_extraction_version ON medical_records_extraction(document_id, version DESC);
CREATE INDEX idx_extraction_is_current ON medical_records_extraction(is_current) WHERE is_current = true;
CREATE INDEX idx_extraction_confidence_level ON medical_records_extraction(confidence_level);
CREATE INDEX idx_extraction_review_required ON medical_records_extraction(manual_review_required) WHERE manual_review_required = true;
CREATE INDEX idx_extraction_search_vector ON medical_records_extraction USING gin(search_vector);
CREATE INDEX idx_extraction_created_at ON medical_records_extraction(created_at DESC);

-- Field Extraction Audit Table
-- Tracks changes to individual fields for compliance
CREATE TABLE field_extraction_audit (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    extraction_id UUID NOT NULL REFERENCES medical_records_extraction(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    
    field_name TEXT NOT NULL,
    old_value TEXT, -- Decrypted for audit purposes
    new_value TEXT, -- Decrypted for audit purposes
    old_confidence DECIMAL(3,2),
    new_confidence DECIMAL(3,2),
    
    change_reason TEXT,
    changed_by UUID NOT NULL REFERENCES user_profiles(id),
    changed_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Access tracking
    ip_address INET,
    user_agent TEXT,
    
    CONSTRAINT field_extraction_audit_confidence_range CHECK (
        (old_confidence IS NULL OR (old_confidence >= 0 AND old_confidence <= 1)) AND
        (new_confidence IS NULL OR (new_confidence >= 0 AND new_confidence <= 1))
    )
);

-- Index for field extraction audit
CREATE INDEX idx_field_audit_extraction_id ON field_extraction_audit(extraction_id);
CREATE INDEX idx_field_audit_organization_id ON field_extraction_audit(organization_id);
CREATE INDEX idx_field_audit_field_name ON field_extraction_audit(field_name);
CREATE INDEX idx_field_audit_changed_at ON field_extraction_audit(changed_at DESC);

-- =====================================================
-- STEP 3: Create Functions for OCR Pipeline
-- =====================================================

-- Function to create new version of extraction
CREATE OR REPLACE FUNCTION create_extraction_version(
    p_document_id UUID,
    p_user_id UUID
) RETURNS UUID AS $$
DECLARE
    v_current_extraction medical_records_extraction%ROWTYPE;
    v_new_id UUID;
BEGIN
    -- Get current extraction
    SELECT * INTO v_current_extraction
    FROM medical_records_extraction
    WHERE document_id = p_document_id
        AND is_current = true
    LIMIT 1;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No current extraction found for document %', p_document_id;
    END IF;
    
    -- Mark current as not current
    UPDATE medical_records_extraction
    SET is_current = false,
        updated_at = NOW()
    WHERE id = v_current_extraction.id;
    
    -- Create new version
    v_new_id := uuid_generate_v4();
    
    INSERT INTO medical_records_extraction (
        id,
        document_id,
        organization_id,
        transaction_id,
        version,
        is_current,
        parent_version_id,
        -- Copy all fields from current version
        patient_name_encrypted,
        patient_name_confidence,
        patient_name_metadata,
        thai_id_encrypted,
        thai_id_confidence,
        thai_id_metadata,
        hospital_id,
        hospital_id_confidence,
        hospital_id_metadata,
        hospital_number_encrypted,
        hospital_number_confidence,
        hospital_number_metadata,
        lab_number_encrypted,
        lab_number_confidence,
        lab_number_metadata,
        age,
        age_confidence,
        date_of_birth_encrypted,
        date_of_birth_confidence,
        sex,
        sex_confidence,
        phone_encrypted,
        phone_confidence,
        place_of_treatment,
        place_of_treatment_confidence,
        referring_physician_encrypted,
        referring_physician_confidence,
        referring_physician_email_encrypted,
        referring_physician_email_confidence,
        blood_collection_dates,
        blood_collection_dates_confidence,
        surgery_biopsy_dates,
        surgery_biopsy_dates_confidence,
        diagnoses,
        diagnoses_confidence,
        medications,
        medications_confidence,
        test_results,
        test_results_confidence,
        overall_confidence_score,
        extracted_text_full,
        detected_languages,
        extraction_warnings,
        cross_reference_notes,
        contextual_clues_used,
        manual_review_required,
        search_vector
    )
    SELECT
        v_new_id,
        document_id,
        organization_id,
        transaction_id,
        version + 1,
        true,
        id,
        -- Copy all fields
        patient_name_encrypted,
        patient_name_confidence,
        patient_name_metadata,
        thai_id_encrypted,
        thai_id_confidence,
        thai_id_metadata,
        hospital_id,
        hospital_id_confidence,
        hospital_id_metadata,
        hospital_number_encrypted,
        hospital_number_confidence,
        hospital_number_metadata,
        lab_number_encrypted,
        lab_number_confidence,
        lab_number_metadata,
        age,
        age_confidence,
        date_of_birth_encrypted,
        date_of_birth_confidence,
        sex,
        sex_confidence,
        phone_encrypted,
        phone_confidence,
        place_of_treatment,
        place_of_treatment_confidence,
        referring_physician_encrypted,
        referring_physician_confidence,
        referring_physician_email_encrypted,
        referring_physician_email_confidence,
        blood_collection_dates,
        blood_collection_dates_confidence,
        surgery_biopsy_dates,
        surgery_biopsy_dates_confidence,
        diagnoses,
        diagnoses_confidence,
        medications,
        medications_confidence,
        test_results,
        test_results_confidence,
        overall_confidence_score,
        extracted_text_full,
        detected_languages,
        extraction_warnings,
        cross_reference_notes,
        contextual_clues_used,
        manual_review_required,
        search_vector
    FROM medical_records_extraction
    WHERE id = v_current_extraction.id;
    
    -- Log audit event
    PERFORM log_audit_event(
        'create'::audit_action,
        'medical_records_extraction',
        v_new_id,
        to_jsonb(v_current_extraction),
        NULL
    );
    
    RETURN v_new_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate overall confidence score
CREATE OR REPLACE FUNCTION calculate_overall_confidence(
    p_extraction_id UUID
) RETURNS DECIMAL(3,2) AS $$
DECLARE
    v_confidence_scores DECIMAL[];
    v_overall_score DECIMAL(3,2);
BEGIN
    -- Collect all non-null confidence scores
    SELECT ARRAY_AGG(confidence) INTO v_confidence_scores
    FROM (
        SELECT patient_name_confidence AS confidence FROM medical_records_extraction WHERE id = p_extraction_id AND patient_name_confidence IS NOT NULL
        UNION ALL
        SELECT thai_id_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND thai_id_confidence IS NOT NULL
        UNION ALL
        SELECT hospital_id_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND hospital_id_confidence IS NOT NULL
        UNION ALL
        SELECT hospital_number_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND hospital_number_confidence IS NOT NULL
        UNION ALL
        SELECT lab_number_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND lab_number_confidence IS NOT NULL
        UNION ALL
        SELECT age_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND age_confidence IS NOT NULL
        UNION ALL
        SELECT date_of_birth_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND date_of_birth_confidence IS NOT NULL
        UNION ALL
        SELECT sex_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND sex_confidence IS NOT NULL
        UNION ALL
        SELECT phone_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND phone_confidence IS NOT NULL
    ) AS scores;
    
    -- Calculate weighted average
    IF array_length(v_confidence_scores, 1) > 0 THEN
        SELECT AVG(score) INTO v_overall_score FROM unnest(v_confidence_scores) AS score;
    ELSE
        v_overall_score := 0.0;
    END IF;
    
    RETURN v_overall_score;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update search vector
CREATE OR REPLACE FUNCTION update_extraction_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := 
        setweight(to_tsvector('simple', COALESCE(NEW.hospital_id, '')), 'A') ||
        setweight(to_tsvector('simple', COALESCE(NEW.place_of_treatment, '')), 'B') ||
        setweight(to_tsvector('simple', COALESCE(NEW.age, '')), 'C') ||
        setweight(to_tsvector('simple', COALESCE(NEW.sex, '')), 'C') ||
        setweight(to_tsvector('simple', COALESCE(NEW.diagnoses::text, '')), 'B') ||
        setweight(to_tsvector('simple', COALESCE(NEW.medications::text, '')), 'C');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 4: Create Triggers
-- =====================================================

-- Update timestamp triggers
CREATE TRIGGER update_ocr_processing_updated_at
    BEFORE UPDATE ON ocr_processing_transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_batch_processing_updated_at
    BEFORE UPDATE ON batch_processing_jobs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_extraction_updated_at
    BEFORE UPDATE ON medical_records_extraction
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Search vector trigger
CREATE TRIGGER update_extraction_search_vector_trigger
    BEFORE INSERT OR UPDATE ON medical_records_extraction
    FOR EACH ROW
    EXECUTE FUNCTION update_extraction_search_vector();

-- Audit triggers
CREATE TRIGGER audit_ocr_processing_transactions
    AFTER INSERT OR UPDATE OR DELETE ON ocr_processing_transactions
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_batch_processing_jobs
    AFTER INSERT OR UPDATE OR DELETE ON batch_processing_jobs
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_medical_records_extraction
    AFTER INSERT OR UPDATE OR DELETE ON medical_records_extraction
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

-- =====================================================
-- STEP 5: Enable Row Level Security
-- =====================================================

ALTER TABLE ocr_processing_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE batch_processing_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_records_extraction ENABLE ROW LEVEL SECURITY;
ALTER TABLE field_extraction_audit ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 6: Create RLS Policies
-- =====================================================

-- OCR Processing Transactions Policies
CREATE POLICY "Users can view OCR transactions in their org"
    ON ocr_processing_transactions FOR SELECT
    USING (
        organization_id = get_user_organization(auth.uid())
    );

CREATE POLICY "Editors can create OCR transactions"
    ON ocr_processing_transactions FOR INSERT
    WITH CHECK (
        organization_id = get_user_organization(auth.uid())
        AND has_role(auth.uid(), 'editor')
    );

CREATE POLICY "Editors can update OCR transactions"
    ON ocr_processing_transactions FOR UPDATE
    USING (
        organization_id = get_user_organization(auth.uid())
        AND has_role(auth.uid(), 'editor')
    );

-- Batch Processing Jobs Policies
CREATE POLICY "Users can view batch jobs in their org"
    ON batch_processing_jobs FOR SELECT
    USING (
        organization_id = get_user_organization(auth.uid())
    );

CREATE POLICY "Editors can manage batch jobs"
    ON batch_processing_jobs FOR ALL
    USING (
        organization_id = get_user_organization(auth.uid())
        AND has_role(auth.uid(), 'editor')
    );

-- Medical Records Extraction Policies
CREATE POLICY "Users can view extractions in their org"
    ON medical_records_extraction FOR SELECT
    USING (
        organization_id = get_user_organization(auth.uid())
        AND (
            has_role(auth.uid(), 'editor')
            OR has_role(auth.uid(), 'viewer')
            OR has_role(auth.uid(), 'analyst')
        )
    );

CREATE POLICY "Editors can create extractions"
    ON medical_records_extraction FOR INSERT
    WITH CHECK (
        organization_id = get_user_organization(auth.uid())
        AND has_role(auth.uid(), 'editor')
    );

CREATE POLICY "Editors can update extractions"
    ON medical_records_extraction FOR UPDATE
    USING (
        organization_id = get_user_organization(auth.uid())
        AND has_role(auth.uid(), 'editor')
    );

-- Field Extraction Audit Policies
CREATE POLICY "Admins can view field audit logs"
    ON field_extraction_audit FOR SELECT
    USING (
        organization_id = get_user_organization(auth.uid())
        AND has_role(auth.uid(), 'admin')
    );

-- =====================================================
-- STEP 7: Create Views for Easier Access
-- =====================================================

-- Current extractions view
CREATE OR REPLACE VIEW current_medical_extractions AS
SELECT 
    e.*,
    d.file_name,
    d.status as document_status,
    d.uploaded_by,
    d.created_at as document_created_at
FROM medical_records_extraction e
JOIN documents d ON e.document_id = d.id
WHERE e.is_current = true
    AND d.deleted_at IS NULL;

-- Processing status dashboard view
CREATE OR REPLACE VIEW ocr_processing_dashboard AS
SELECT 
    o.name as organization_name,
    COUNT(DISTINCT t.id) as total_transactions,
    COUNT(DISTINCT CASE WHEN t.status = 'completed' THEN t.id END) as completed_transactions,
    COUNT(DISTINCT CASE WHEN t.status = 'failed' THEN t.id END) as failed_transactions,
    COUNT(DISTINCT CASE WHEN t.status = 'processing' THEN t.id END) as active_transactions,
    AVG(t.processing_duration_ms) as avg_processing_time_ms,
    SUM(t.tokens_used) as total_tokens_used,
    SUM(t.api_cost_usd) as total_api_cost
FROM organizations o
LEFT JOIN ocr_processing_transactions t ON o.id = t.organization_id
GROUP BY o.id, o.name;

-- Grant permissions on views
GRANT SELECT ON current_medical_extractions TO authenticated;
GRANT SELECT ON ocr_processing_dashboard TO authenticated;

-- =====================================================
-- STEP 8: Create Helper Functions for OCR Pipeline
-- =====================================================

-- Function to get next document for processing
CREATE OR REPLACE FUNCTION get_next_ocr_document(
    p_organization_id UUID,
    p_batch_id UUID DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_document_id UUID;
BEGIN
    -- Get next unprocessed document
    SELECT d.id INTO v_document_id
    FROM documents d
    LEFT JOIN ocr_processing_transactions t ON d.id = t.document_id 
        AND t.status IN ('completed', 'processing')
    WHERE d.organization_id = p_organization_id
        AND d.status = 'processing'
        AND d.deleted_at IS NULL
        AND t.id IS NULL
        AND (p_batch_id IS NULL OR EXISTS (
            SELECT 1 FROM batch_processing_jobs b 
            WHERE b.id = p_batch_id 
            AND b.organization_id = p_organization_id
        ))
    ORDER BY d.created_at ASC
    LIMIT 1
    FOR UPDATE SKIP LOCKED;
    
    RETURN v_document_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update batch progress
CREATE OR REPLACE FUNCTION update_batch_progress(
    p_batch_id UUID
) RETURNS VOID AS $$
DECLARE
    v_stats RECORD;
BEGIN
    -- Calculate batch statistics
    SELECT 
        COUNT(DISTINCT t.document_id) as processed,
        COUNT(DISTINCT CASE WHEN t.status = 'completed' THEN t.document_id END) as successful,
        COUNT(DISTINCT CASE WHEN t.status = 'failed' THEN t.document_id END) as failed
    INTO v_stats
    FROM ocr_processing_transactions t
    WHERE t.batch_id = p_batch_id;
    
    -- Update batch job
    UPDATE batch_processing_jobs
    SET 
        processed_documents = v_stats.processed,
        successful_documents = v_stats.successful,
        failed_documents = v_stats.failed,
        progress_percentage = CASE 
            WHEN total_documents > 0 
            THEN (v_stats.processed::DECIMAL / total_documents * 100)
            ELSE 0 
        END,
        status = CASE
            WHEN v_stats.processed = 0 THEN 'pending'
            WHEN v_stats.processed < total_documents THEN 'running'
            WHEN v_stats.failed > 0 THEN 'partially_completed'
            ELSE 'completed'
        END,
        completed_at = CASE
            WHEN v_stats.processed >= total_documents THEN NOW()
            ELSE NULL
        END
    WHERE id = p_batch_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if all tables were created
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN (
    'ocr_processing_transactions', 
    'batch_processing_jobs', 
    'medical_records_extraction',
    'field_extraction_audit'
  )
ORDER BY table_name;

-- Check if RLS is enabled on new tables
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN (
    'ocr_processing_transactions', 
    'batch_processing_jobs', 
    'medical_records_extraction',
    'field_extraction_audit'
  );

-- Check new policies
SELECT schemaname, tablename, policyname 
FROM pg_policies 
WHERE schemaname = 'public'
  AND tablename IN (
    'ocr_processing_transactions', 
    'batch_processing_jobs', 
    'medical_records_extraction',
    'field_extraction_audit'
  )
ORDER BY tablename, policyname;