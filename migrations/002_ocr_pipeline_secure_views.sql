-- ChromoForge OCR Pipeline Secure Views Migration
-- Version: 002
-- Description: Creates secure views with PII decryption for authorized users
-- Author: System Architect
-- Date: 2025-01-04

-- =====================================================
-- STEP 1: Create Secure Views for Medical Extractions
-- =====================================================

-- Secure view for medical extractions with conditional PII decryption
CREATE OR REPLACE VIEW medical_extractions_secure AS
SELECT 
    e.id,
    e.document_id,
    e.organization_id,
    e.transaction_id,
    e.version,
    e.is_current,
    e.parent_version_id,
    
    -- Conditionally decrypt PII based on user role
    CASE 
        WHEN has_role(auth.uid(), 'editor') OR has_role(auth.uid(), 'admin') THEN 
            decrypt_pii(e.patient_name_encrypted, e.organization_id)
        ELSE '***REDACTED***'
    END as patient_name,
    e.patient_name_confidence,
    e.patient_name_metadata,
    
    CASE 
        WHEN has_role(auth.uid(), 'editor') OR has_role(auth.uid(), 'admin') THEN 
            decrypt_pii(e.thai_id_encrypted, e.organization_id)
        ELSE '***REDACTED***'
    END as thai_id,
    e.thai_id_confidence,
    e.thai_id_metadata,
    
    -- Non-PII fields
    e.hospital_id,
    e.hospital_id_confidence,
    e.hospital_id_metadata,
    
    CASE 
        WHEN has_role(auth.uid(), 'editor') OR has_role(auth.uid(), 'admin') THEN 
            decrypt_pii(e.hospital_number_encrypted, e.organization_id)
        ELSE '***REDACTED***'
    END as hospital_number,
    e.hospital_number_confidence,
    e.hospital_number_metadata,
    
    CASE 
        WHEN has_role(auth.uid(), 'editor') OR has_role(auth.uid(), 'admin') THEN 
            decrypt_pii(e.lab_number_encrypted, e.organization_id)
        ELSE '***REDACTED***'
    END as lab_number,
    e.lab_number_confidence,
    e.lab_number_metadata,
    
    e.age,
    e.age_confidence,
    
    CASE 
        WHEN has_role(auth.uid(), 'editor') OR has_role(auth.uid(), 'admin') THEN 
            decrypt_pii(e.date_of_birth_encrypted, e.organization_id)
        ELSE '***REDACTED***'
    END as date_of_birth,
    e.date_of_birth_confidence,
    
    e.sex,
    e.sex_confidence,
    
    CASE 
        WHEN has_role(auth.uid(), 'editor') OR has_role(auth.uid(), 'admin') THEN 
            decrypt_pii(e.phone_encrypted, e.organization_id)
        ELSE '***REDACTED***'
    END as phone,
    e.phone_confidence,
    
    e.place_of_treatment,
    e.place_of_treatment_confidence,
    
    CASE 
        WHEN has_role(auth.uid(), 'editor') OR has_role(auth.uid(), 'admin') THEN 
            decrypt_pii(e.referring_physician_encrypted, e.organization_id)
        ELSE '***REDACTED***'
    END as referring_physician,
    e.referring_physician_confidence,
    
    CASE 
        WHEN has_role(auth.uid(), 'editor') OR has_role(auth.uid(), 'admin') THEN 
            decrypt_pii(e.referring_physician_email_encrypted, e.organization_id)
        ELSE '***REDACTED***'
    END as referring_physician_email,
    e.referring_physician_email_confidence,
    
    e.blood_collection_dates,
    e.blood_collection_dates_confidence,
    
    e.surgery_biopsy_dates,
    e.surgery_biopsy_dates_confidence,
    
    e.diagnoses,
    e.diagnoses_confidence,
    
    e.medications,
    e.medications_confidence,
    
    e.test_results,
    e.test_results_confidence,
    
    e.overall_confidence_score,
    e.confidence_level,
    
    -- Only show full extracted text to editors/admins
    CASE 
        WHEN has_role(auth.uid(), 'editor') OR has_role(auth.uid(), 'admin') THEN 
            e.extracted_text_full
        ELSE NULL
    END as extracted_text_full,
    
    e.detected_languages,
    e.extraction_warnings,
    e.cross_reference_notes,
    e.contextual_clues_used,
    
    e.manual_review_required,
    e.reviewed_by,
    e.reviewed_at,
    e.review_notes,
    
    e.created_at,
    e.updated_at,
    
    -- Document information
    d.file_name,
    d.status as document_status,
    d.page_count,
    d.processing_completed_at,
    
    -- User information
    u.full_name as uploaded_by_name,
    r.full_name as reviewed_by_name
FROM medical_records_extraction e
JOIN documents d ON e.document_id = d.id
LEFT JOIN user_profiles u ON d.uploaded_by = u.id
LEFT JOIN user_profiles r ON e.reviewed_by = r.id
WHERE e.organization_id = get_user_organization(auth.uid())
    AND d.deleted_at IS NULL;

-- Grant access to secure view
GRANT SELECT ON medical_extractions_secure TO authenticated;

-- =====================================================
-- STEP 2: Create Aggregated Views for Analytics
-- =====================================================

-- Extraction quality analytics view
CREATE OR REPLACE VIEW extraction_quality_analytics AS
SELECT 
    o.name as organization_name,
    DATE_TRUNC('day', e.created_at) as extraction_date,
    COUNT(*) as total_extractions,
    COUNT(CASE WHEN e.confidence_level = 'high' THEN 1 END) as high_confidence_count,
    COUNT(CASE WHEN e.confidence_level = 'medium' THEN 1 END) as medium_confidence_count,
    COUNT(CASE WHEN e.confidence_level = 'low' THEN 1 END) as low_confidence_count,
    COUNT(CASE WHEN e.confidence_level = 'very_low' THEN 1 END) as very_low_confidence_count,
    AVG(e.overall_confidence_score) as avg_confidence_score,
    COUNT(CASE WHEN e.manual_review_required THEN 1 END) as review_required_count,
    COUNT(CASE WHEN e.reviewed_by IS NOT NULL THEN 1 END) as reviewed_count,
    
    -- Field-level confidence averages
    AVG(e.patient_name_confidence) as avg_patient_name_confidence,
    AVG(e.thai_id_confidence) as avg_thai_id_confidence,
    AVG(e.hospital_id_confidence) as avg_hospital_id_confidence,
    AVG(e.hospital_number_confidence) as avg_hospital_number_confidence,
    AVG(e.lab_number_confidence) as avg_lab_number_confidence,
    
    -- Extraction coverage
    COUNT(CASE WHEN e.patient_name_encrypted IS NOT NULL THEN 1 END)::DECIMAL / COUNT(*) as patient_name_coverage,
    COUNT(CASE WHEN e.thai_id_encrypted IS NOT NULL THEN 1 END)::DECIMAL / COUNT(*) as thai_id_coverage,
    COUNT(CASE WHEN e.hospital_id IS NOT NULL THEN 1 END)::DECIMAL / COUNT(*) as hospital_id_coverage,
    
    -- Warning and error rates
    AVG(JSONB_ARRAY_LENGTH(e.extraction_warnings)) as avg_warnings_per_extraction
FROM medical_records_extraction e
JOIN organizations o ON e.organization_id = o.id
WHERE e.is_current = true
    AND e.organization_id = get_user_organization(auth.uid())
GROUP BY o.name, DATE_TRUNC('day', e.created_at)
ORDER BY extraction_date DESC;

-- Grant access to analytics view
GRANT SELECT ON extraction_quality_analytics TO authenticated;

-- =====================================================
-- STEP 3: Create Processing Performance Views
-- =====================================================

-- OCR processing performance view
CREATE OR REPLACE VIEW ocr_processing_performance AS
SELECT 
    t.organization_id,
    DATE_TRUNC('hour', t.created_at) as processing_hour,
    COUNT(*) as total_attempts,
    COUNT(DISTINCT t.document_id) as unique_documents,
    
    -- Status distribution
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as successful_attempts,
    COUNT(CASE WHEN t.status = 'failed' THEN 1 END) as failed_attempts,
    COUNT(CASE WHEN t.status = 'processing' THEN 1 END) as in_progress,
    COUNT(CASE WHEN t.status = 'retry_pending' THEN 1 END) as pending_retry,
    
    -- Performance metrics
    AVG(t.processing_duration_ms) as avg_processing_time_ms,
    MIN(t.processing_duration_ms) as min_processing_time_ms,
    MAX(t.processing_duration_ms) as max_processing_time_ms,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY t.processing_duration_ms) as median_processing_time_ms,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY t.processing_duration_ms) as p95_processing_time_ms,
    
    -- Resource usage
    SUM(t.tokens_used) as total_tokens,
    AVG(t.tokens_used) as avg_tokens_per_document,
    SUM(t.api_cost_usd) as total_cost_usd,
    
    -- Retry statistics
    AVG(t.attempt_number) as avg_attempts_per_document,
    MAX(t.attempt_number) as max_attempts,
    COUNT(CASE WHEN t.attempt_number > 1 THEN 1 END) as retry_count,
    
    -- Error analysis
    COUNT(DISTINCT t.error_code) as unique_error_codes,
    MODE() WITHIN GROUP (ORDER BY t.error_code) as most_common_error
FROM ocr_processing_transactions t
WHERE t.organization_id = get_user_organization(auth.uid())
GROUP BY t.organization_id, DATE_TRUNC('hour', t.created_at)
ORDER BY processing_hour DESC;

-- Grant access to performance view
GRANT SELECT ON ocr_processing_performance TO authenticated;

-- =====================================================
-- STEP 4: Create Batch Processing Monitoring Views
-- =====================================================

-- Active batch jobs view
CREATE OR REPLACE VIEW active_batch_jobs AS
SELECT 
    b.id,
    b.name,
    b.description,
    b.status,
    b.total_documents,
    b.processed_documents,
    b.successful_documents,
    b.failed_documents,
    b.progress_percentage,
    b.priority,
    b.started_at,
    b.estimated_completion_at,
    
    -- Calculate estimated time remaining
    CASE 
        WHEN b.processed_documents > 0 AND b.status = 'running' THEN
            EXTRACT(EPOCH FROM (NOW() - b.started_at)) * 
            (b.total_documents - b.processed_documents) / b.processed_documents * INTERVAL '1 second'
        ELSE NULL
    END as estimated_time_remaining,
    
    -- Processing rate
    CASE 
        WHEN b.processed_documents > 0 AND b.started_at IS NOT NULL THEN
            b.processed_documents / EXTRACT(EPOCH FROM (NOW() - b.started_at)) * 60
        ELSE 0
    END as documents_per_minute,
    
    -- Success rate
    CASE 
        WHEN b.processed_documents > 0 THEN
            b.successful_documents::DECIMAL / b.processed_documents * 100
        ELSE 0
    END as success_rate,
    
    -- Current document being processed
    d.file_name as current_document_name,
    
    -- Creator information
    u.full_name as created_by_name,
    b.created_at
FROM batch_processing_jobs b
LEFT JOIN documents d ON b.current_document_id = d.id
LEFT JOIN user_profiles u ON b.created_by = u.id
WHERE b.organization_id = get_user_organization(auth.uid())
    AND b.status IN ('pending', 'running')
ORDER BY b.priority DESC, b.created_at ASC;

-- Grant access to batch monitoring view
GRANT SELECT ON active_batch_jobs TO authenticated;

-- =====================================================
-- STEP 5: Create Field-Level Change History View
-- =====================================================

-- Field change history view
CREATE OR REPLACE VIEW field_change_history AS
SELECT 
    f.id,
    f.extraction_id,
    e.document_id,
    d.file_name,
    f.field_name,
    
    -- Mask PII values based on user role
    CASE 
        WHEN has_role(auth.uid(), 'admin') AND 
             f.field_name IN ('patient_name', 'thai_id', 'hospital_number', 'lab_number', 
                              'date_of_birth', 'phone', 'referring_physician', 
                              'referring_physician_email') THEN 
            f.old_value
        WHEN f.field_name IN ('patient_name', 'thai_id', 'hospital_number', 'lab_number', 
                              'date_of_birth', 'phone', 'referring_physician', 
                              'referring_physician_email') THEN 
            '***REDACTED***'
        ELSE f.old_value
    END as old_value,
    
    CASE 
        WHEN has_role(auth.uid(), 'admin') AND 
             f.field_name IN ('patient_name', 'thai_id', 'hospital_number', 'lab_number', 
                              'date_of_birth', 'phone', 'referring_physician', 
                              'referring_physician_email') THEN 
            f.new_value
        WHEN f.field_name IN ('patient_name', 'thai_id', 'hospital_number', 'lab_number', 
                              'date_of_birth', 'phone', 'referring_physician', 
                              'referring_physician_email') THEN 
            '***REDACTED***'
        ELSE f.new_value
    END as new_value,
    
    f.old_confidence,
    f.new_confidence,
    f.change_reason,
    
    u.full_name as changed_by_name,
    f.changed_at,
    f.ip_address
FROM field_extraction_audit f
JOIN medical_records_extraction e ON f.extraction_id = e.id
JOIN documents d ON e.document_id = d.id
JOIN user_profiles u ON f.changed_by = u.id
WHERE f.organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'admin')
ORDER BY f.changed_at DESC;

-- Grant access to field history view (admins only)
GRANT SELECT ON field_change_history TO authenticated;

-- =====================================================
-- STEP 6: Create Search Functions
-- =====================================================

-- Function to search medical extractions
CREATE OR REPLACE FUNCTION search_medical_extractions(
    p_search_query TEXT,
    p_confidence_threshold DECIMAL DEFAULT 0.5,
    p_include_low_confidence BOOLEAN DEFAULT false,
    p_limit INTEGER DEFAULT 100
) RETURNS TABLE (
    extraction_id UUID,
    document_id UUID,
    file_name TEXT,
    overall_confidence DECIMAL,
    rank REAL,
    headline TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.id as extraction_id,
        e.document_id,
        d.file_name,
        e.overall_confidence_score as overall_confidence,
        ts_rank(e.search_vector, websearch_to_tsquery('simple', p_search_query)) as rank,
        ts_headline(
            'simple',
            COALESCE(e.place_of_treatment, '') || ' ' || 
            COALESCE(e.diagnoses::text, '') || ' ' ||
            COALESCE(e.medications::text, ''),
            websearch_to_tsquery('simple', p_search_query),
            'MaxWords=50, MinWords=20'
        ) as headline
    FROM medical_records_extraction e
    JOIN documents d ON e.document_id = d.id
    WHERE e.organization_id = get_user_organization(auth.uid())
        AND e.is_current = true
        AND d.deleted_at IS NULL
        AND e.search_vector @@ websearch_to_tsquery('simple', p_search_query)
        AND (p_include_low_confidence OR e.overall_confidence_score >= p_confidence_threshold)
    ORDER BY rank DESC, e.overall_confidence_score DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 7: Create Utility Functions for Reporting
-- =====================================================

-- Function to get extraction statistics by date range
CREATE OR REPLACE FUNCTION get_extraction_statistics(
    p_start_date DATE,
    p_end_date DATE,
    p_organization_id UUID DEFAULT NULL
) RETURNS TABLE (
    metric_name TEXT,
    metric_value NUMERIC
) AS $$
BEGIN
    -- Use current user's organization if not specified
    p_organization_id := COALESCE(p_organization_id, get_user_organization(auth.uid()));
    
    RETURN QUERY
    WITH stats AS (
        SELECT 
            COUNT(*) as total_extractions,
            COUNT(CASE WHEN overall_confidence_score > 0.9 THEN 1 END) as high_confidence,
            COUNT(CASE WHEN overall_confidence_score < 0.5 THEN 1 END) as low_confidence,
            COUNT(CASE WHEN manual_review_required THEN 1 END) as review_required,
            COUNT(CASE WHEN reviewed_by IS NOT NULL THEN 1 END) as reviewed,
            AVG(overall_confidence_score) as avg_confidence,
            COUNT(DISTINCT document_id) as unique_documents
        FROM medical_records_extraction
        WHERE organization_id = p_organization_id
            AND is_current = true
            AND DATE(created_at) BETWEEN p_start_date AND p_end_date
    ),
    processing_stats AS (
        SELECT 
            COUNT(*) as total_processing_attempts,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_processing,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_processing,
            AVG(processing_duration_ms) as avg_processing_time,
            SUM(tokens_used) as total_tokens,
            SUM(api_cost_usd) as total_cost
        FROM ocr_processing_transactions
        WHERE organization_id = p_organization_id
            AND DATE(created_at) BETWEEN p_start_date AND p_end_date
    )
    SELECT 'Total Extractions'::TEXT, total_extractions::NUMERIC FROM stats
    UNION ALL
    SELECT 'High Confidence (>90%)', high_confidence::NUMERIC FROM stats
    UNION ALL
    SELECT 'Low Confidence (<50%)', low_confidence::NUMERIC FROM stats
    UNION ALL
    SELECT 'Review Required', review_required::NUMERIC FROM stats
    UNION ALL
    SELECT 'Reviewed', reviewed::NUMERIC FROM stats
    UNION ALL
    SELECT 'Average Confidence', ROUND(avg_confidence::NUMERIC, 3) FROM stats
    UNION ALL
    SELECT 'Unique Documents', unique_documents::NUMERIC FROM stats
    UNION ALL
    SELECT 'Processing Attempts', total_processing_attempts::NUMERIC FROM processing_stats
    UNION ALL
    SELECT 'Successful Processing', successful_processing::NUMERIC FROM processing_stats
    UNION ALL
    SELECT 'Failed Processing', failed_processing::NUMERIC FROM processing_stats
    UNION ALL
    SELECT 'Avg Processing Time (ms)', ROUND(avg_processing_time::NUMERIC, 0) FROM processing_stats
    UNION ALL
    SELECT 'Total Tokens Used', total_tokens::NUMERIC FROM processing_stats
    UNION ALL
    SELECT 'Total API Cost (USD)', ROUND(total_cost::NUMERIC, 2) FROM processing_stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- List all views created
SELECT viewname 
FROM pg_views 
WHERE schemaname = 'public' 
  AND viewname IN (
    'medical_extractions_secure',
    'extraction_quality_analytics',
    'ocr_processing_performance',
    'active_batch_jobs',
    'field_change_history'
  )
ORDER BY viewname;

-- Check view permissions
SELECT 
    schemaname,
    viewname,
    viewowner,
    hasindexes,
    hasrules,
    hastriggers
FROM pg_views
WHERE schemaname = 'public'
  AND viewname LIKE '%extraction%' OR viewname LIKE '%ocr%' OR viewname LIKE '%batch%'
ORDER BY viewname;