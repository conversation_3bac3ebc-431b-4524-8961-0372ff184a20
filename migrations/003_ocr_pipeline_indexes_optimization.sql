-- ChromoForge OCR Pipeline Index Optimization Migration
-- Version: 003
-- Description: Creates optimized indexes for performance and adds monitoring capabilities
-- Author: System Architect
-- Date: 2025-01-04

-- =====================================================
-- STEP 1: Create Specialized Indexes for Common Queries
-- =====================================================

-- Composite indexes for extraction queries
CREATE INDEX idx_extraction_org_current_confidence 
    ON medical_records_extraction(organization_id, is_current, overall_confidence_score DESC)
    WHERE is_current = true;

CREATE INDEX idx_extraction_org_review_required 
    ON medical_records_extraction(organization_id, manual_review_required, created_at DESC)
    WHERE is_current = true AND manual_review_required = true;

CREATE INDEX idx_extraction_document_version 
    ON medical_records_extraction(document_id, version DESC, is_current);

-- Indexes for date-based queries
CREATE INDEX idx_extraction_created_date 
    ON medical_records_extraction(organization_id, DATE(created_at), overall_confidence_score DESC)
    WHERE is_current = true;

-- Indexes for specific field searches
CREATE INDEX idx_extraction_hospital_id 
    ON medical_records_extraction(organization_id, hospital_id)
    WHERE is_current = true AND hospital_id IS NOT NULL;

CREATE INDEX idx_extraction_confidence_level 
    ON medical_records_extraction(organization_id, confidence_level)
    WHERE is_current = true;

-- =====================================================
-- STEP 2: Create Indexes for OCR Processing Performance
-- =====================================================

-- Composite index for finding documents to process
CREATE INDEX idx_ocr_processing_status_retry 
    ON ocr_processing_transactions(status, retry_after, organization_id)
    WHERE status IN ('queued', 'retry_pending');

-- Index for batch processing queries
CREATE INDEX idx_ocr_processing_batch_status 
    ON ocr_processing_transactions(batch_id, status, created_at)
    WHERE batch_id IS NOT NULL;

-- Index for error analysis
CREATE INDEX idx_ocr_processing_errors 
    ON ocr_processing_transactions(organization_id, error_code, created_at DESC)
    WHERE error_code IS NOT NULL;

-- Index for cost tracking
CREATE INDEX idx_ocr_processing_cost 
    ON ocr_processing_transactions(organization_id, DATE(created_at), api_cost_usd)
    WHERE api_cost_usd IS NOT NULL;

-- =====================================================
-- STEP 3: Create Indexes for Batch Processing
-- =====================================================

-- Index for active batch monitoring
CREATE INDEX idx_batch_active_priority 
    ON batch_processing_jobs(status, priority DESC, created_at)
    WHERE status IN ('pending', 'running');

-- Index for batch completion tracking
CREATE INDEX idx_batch_completion 
    ON batch_processing_jobs(organization_id, completed_at DESC)
    WHERE completed_at IS NOT NULL;

-- =====================================================
-- STEP 4: Create Partial Indexes for Specific Workflows
-- =====================================================

-- Index for unprocessed documents
CREATE INDEX idx_documents_unprocessed 
    ON documents(organization_id, created_at, status)
    WHERE status = 'processing' 
    AND deleted_at IS NULL
    AND NOT EXISTS (
        SELECT 1 FROM ocr_processing_transactions t 
        WHERE t.document_id = documents.id 
        AND t.status IN ('completed', 'processing')
    );

-- Index for documents requiring review
CREATE INDEX idx_documents_review_needed 
    ON documents d(organization_id, created_at DESC)
    WHERE EXISTS (
        SELECT 1 FROM medical_records_extraction e
        WHERE e.document_id = d.id
        AND e.is_current = true
        AND e.manual_review_required = true
        AND e.reviewed_by IS NULL
    );

-- =====================================================
-- STEP 5: Create Materialized Views for Analytics
-- =====================================================

-- Daily extraction statistics materialized view
CREATE MATERIALIZED VIEW daily_extraction_stats AS
SELECT 
    organization_id,
    DATE(created_at) as extraction_date,
    COUNT(*) as total_extractions,
    COUNT(CASE WHEN confidence_level = 'high' THEN 1 END) as high_confidence,
    COUNT(CASE WHEN confidence_level = 'medium' THEN 1 END) as medium_confidence,
    COUNT(CASE WHEN confidence_level = 'low' THEN 1 END) as low_confidence,
    COUNT(CASE WHEN confidence_level = 'very_low' THEN 1 END) as very_low_confidence,
    AVG(overall_confidence_score) as avg_confidence,
    
    -- Field extraction rates
    COUNT(patient_name_encrypted) * 100.0 / COUNT(*) as patient_name_rate,
    COUNT(thai_id_encrypted) * 100.0 / COUNT(*) as thai_id_rate,
    COUNT(hospital_id) * 100.0 / COUNT(*) as hospital_id_rate,
    
    -- Review statistics
    COUNT(CASE WHEN manual_review_required THEN 1 END) as review_required,
    COUNT(CASE WHEN reviewed_by IS NOT NULL THEN 1 END) as reviewed,
    
    -- Performance metrics
    AVG(CASE WHEN patient_name_confidence IS NOT NULL THEN patient_name_confidence END) as avg_name_confidence,
    AVG(CASE WHEN thai_id_confidence IS NOT NULL THEN thai_id_confidence END) as avg_id_confidence
FROM medical_records_extraction
WHERE is_current = true
GROUP BY organization_id, DATE(created_at);

-- Create indexes on materialized view
CREATE INDEX idx_daily_stats_org_date 
    ON daily_extraction_stats(organization_id, extraction_date DESC);

-- Processing performance materialized view
CREATE MATERIALIZED VIEW hourly_processing_stats AS
SELECT 
    organization_id,
    DATE_TRUNC('hour', created_at) as processing_hour,
    COUNT(*) as total_attempts,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
    AVG(processing_duration_ms) as avg_duration_ms,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY processing_duration_ms) as median_duration_ms,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY processing_duration_ms) as p95_duration_ms,
    SUM(tokens_used) as total_tokens,
    SUM(api_cost_usd) as total_cost
FROM ocr_processing_transactions
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY organization_id, DATE_TRUNC('hour', created_at);

-- Create indexes on materialized view
CREATE INDEX idx_hourly_stats_org_hour 
    ON hourly_processing_stats(organization_id, processing_hour DESC);

-- =====================================================
-- STEP 6: Create Monitoring Functions
-- =====================================================

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_ocr_statistics()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY daily_extraction_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY hourly_processing_stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to analyze index usage
CREATE OR REPLACE FUNCTION analyze_index_usage()
RETURNS TABLE (
    index_name TEXT,
    table_name TEXT,
    index_size TEXT,
    index_scans BIGINT,
    index_reads BIGINT,
    index_efficiency DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        indexrelname::TEXT as index_name,
        relname::TEXT as table_name,
        pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
        idx_scan as index_scans,
        idx_tup_read as index_reads,
        CASE 
            WHEN idx_scan > 0 THEN 
                ROUND((idx_tup_read::DECIMAL / idx_scan), 2)
            ELSE 0 
        END as index_efficiency
    FROM pg_stat_user_indexes
    WHERE schemaname = 'public'
        AND (relname LIKE '%ocr%' OR relname LIKE '%extraction%' OR relname LIKE '%batch%')
    ORDER BY idx_scan DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to identify slow queries
CREATE OR REPLACE FUNCTION identify_slow_ocr_queries(
    p_duration_threshold INTERVAL DEFAULT '1 second'
) RETURNS TABLE (
    query_text TEXT,
    calls BIGINT,
    total_time DOUBLE PRECISION,
    mean_time DOUBLE PRECISION,
    max_time DOUBLE PRECISION
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        LEFT(query, 100) || '...' as query_text,
        calls,
        total_exec_time as total_time,
        mean_exec_time as mean_time,
        max_exec_time as max_time
    FROM pg_stat_statements
    WHERE query ILIKE '%medical_records_extraction%'
        OR query ILIKE '%ocr_processing%'
        OR query ILIKE '%batch_processing%'
    AND mean_exec_time > EXTRACT(MILLISECONDS FROM p_duration_threshold)
    ORDER BY mean_exec_time DESC
    LIMIT 20;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 7: Create Performance Monitoring Tables
-- =====================================================

-- Table to track query performance over time
CREATE TABLE ocr_query_performance_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_fingerprint TEXT NOT NULL,
    query_sample TEXT,
    execution_count BIGINT NOT NULL,
    total_time_ms DOUBLE PRECISION NOT NULL,
    mean_time_ms DOUBLE PRECISION NOT NULL,
    max_time_ms DOUBLE PRECISION NOT NULL,
    logged_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_query_performance_logged_at 
    ON ocr_query_performance_log(logged_at DESC);

CREATE INDEX idx_query_performance_fingerprint 
    ON ocr_query_performance_log(query_fingerprint, logged_at DESC);

-- Table to track index usage over time
CREATE TABLE ocr_index_usage_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    index_name TEXT NOT NULL,
    table_name TEXT NOT NULL,
    index_size_bytes BIGINT NOT NULL,
    index_scans BIGINT NOT NULL,
    index_reads BIGINT NOT NULL,
    logged_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_index_usage_logged_at 
    ON ocr_index_usage_log(logged_at DESC);

CREATE INDEX idx_index_usage_name 
    ON ocr_index_usage_log(index_name, logged_at DESC);

-- =====================================================
-- STEP 8: Create Scheduled Maintenance Functions
-- =====================================================

-- Function to vacuum and analyze OCR tables
CREATE OR REPLACE FUNCTION maintain_ocr_tables()
RETURNS void AS $$
BEGIN
    -- Vacuum and analyze main tables
    VACUUM ANALYZE medical_records_extraction;
    VACUUM ANALYZE ocr_processing_transactions;
    VACUUM ANALYZE batch_processing_jobs;
    VACUUM ANALYZE documents;
    
    -- Update table statistics
    ANALYZE medical_records_extraction;
    ANALYZE ocr_processing_transactions;
    ANALYZE batch_processing_jobs;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log performance metrics
CREATE OR REPLACE FUNCTION log_ocr_performance_metrics()
RETURNS void AS $$
BEGIN
    -- Log query performance
    INSERT INTO ocr_query_performance_log (
        query_fingerprint,
        query_sample,
        execution_count,
        total_time_ms,
        mean_time_ms,
        max_time_ms
    )
    SELECT 
        queryid::TEXT,
        LEFT(query, 200),
        calls,
        total_exec_time,
        mean_exec_time,
        max_exec_time
    FROM pg_stat_statements
    WHERE (query ILIKE '%medical_records_extraction%'
        OR query ILIKE '%ocr_processing%'
        OR query ILIKE '%batch_processing%')
        AND calls > 10;
    
    -- Log index usage
    INSERT INTO ocr_index_usage_log (
        index_name,
        table_name,
        index_size_bytes,
        index_scans,
        index_reads
    )
    SELECT 
        indexrelname::TEXT,
        relname::TEXT,
        pg_relation_size(indexrelid),
        idx_scan,
        idx_tup_read
    FROM pg_stat_user_indexes
    WHERE schemaname = 'public'
        AND (relname LIKE '%ocr%' OR relname LIKE '%extraction%' OR relname LIKE '%batch%');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 9: Create Performance Dashboard View
-- =====================================================

CREATE OR REPLACE VIEW ocr_performance_dashboard AS
WITH recent_processing AS (
    SELECT 
        organization_id,
        COUNT(*) as attempts_last_hour,
        AVG(processing_duration_ms) as avg_duration_last_hour,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failures_last_hour
    FROM ocr_processing_transactions
    WHERE created_at >= NOW() - INTERVAL '1 hour'
    GROUP BY organization_id
),
active_batches AS (
    SELECT 
        organization_id,
        COUNT(*) as active_batch_count,
        SUM(total_documents - processed_documents) as pending_documents
    FROM batch_processing_jobs
    WHERE status IN ('pending', 'running')
    GROUP BY organization_id
),
extraction_quality AS (
    SELECT 
        organization_id,
        AVG(overall_confidence_score) as avg_confidence_today,
        COUNT(CASE WHEN manual_review_required THEN 1 END) as pending_reviews
    FROM medical_records_extraction
    WHERE is_current = true
        AND DATE(created_at) = CURRENT_DATE
    GROUP BY organization_id
)
SELECT 
    o.name as organization_name,
    COALESCE(rp.attempts_last_hour, 0) as processing_attempts_last_hour,
    COALESCE(rp.avg_duration_last_hour, 0) as avg_processing_ms_last_hour,
    COALESCE(rp.failures_last_hour, 0) as failures_last_hour,
    COALESCE(ab.active_batch_count, 0) as active_batches,
    COALESCE(ab.pending_documents, 0) as pending_batch_documents,
    COALESCE(eq.avg_confidence_today, 0) as avg_confidence_today,
    COALESCE(eq.pending_reviews, 0) as documents_pending_review
FROM organizations o
LEFT JOIN recent_processing rp ON o.id = rp.organization_id
LEFT JOIN active_batches ab ON o.id = ab.organization_id
LEFT JOIN extraction_quality eq ON o.id = eq.organization_id
WHERE o.id = get_user_organization(auth.uid());

-- Grant access to performance dashboard
GRANT SELECT ON ocr_performance_dashboard TO authenticated;

-- =====================================================
-- STEP 10: Create Cleanup Functions
-- =====================================================

-- Function to archive old processing logs
CREATE OR REPLACE FUNCTION archive_old_ocr_logs(
    p_days_to_keep INTEGER DEFAULT 90
) RETURNS INTEGER AS $$
DECLARE
    v_archived_count INTEGER;
BEGIN
    -- Archive old processing transactions
    WITH archived AS (
        DELETE FROM ocr_processing_transactions
        WHERE created_at < NOW() - (p_days_to_keep || ' days')::INTERVAL
            AND status IN ('completed', 'failed')
        RETURNING *
    )
    SELECT COUNT(*) INTO v_archived_count FROM archived;
    
    -- Clean up old performance logs
    DELETE FROM ocr_query_performance_log
    WHERE logged_at < NOW() - INTERVAL '30 days';
    
    DELETE FROM ocr_index_usage_log
    WHERE logged_at < NOW() - INTERVAL '30 days';
    
    RETURN v_archived_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check all indexes created
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
    AND (tablename LIKE '%ocr%' OR tablename LIKE '%extraction%' OR tablename LIKE '%batch%')
    AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- Check materialized views
SELECT 
    schemaname,
    matviewname,
    matviewowner,
    hasindexes
FROM pg_matviews
WHERE schemaname = 'public'
ORDER BY matviewname;