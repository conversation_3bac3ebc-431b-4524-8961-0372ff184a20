-- ChromoForge Generic Medical Record Schema Migration
-- Version: 004
-- Description: Updates medical records extraction to use generic 13-field schema
-- Author: System Architect  
-- Date: 2025-01-04

-- =====================================================
-- STEP 1: Add New Generic Schema Fields
-- =====================================================

-- Add new generic medical record fields to medical_records_extraction table
ALTER TABLE medical_records_extraction
ADD COLUMN patient_code TEXT,
ADD COLUMN patient_code_confidence DECIMAL(3,2),
ADD COLUMN patient_code_metadata JSONB DEFAULT '{}',

ADD COLUMN sample_code TEXT,
ADD COLUMN sample_code_confidence DECIMAL(3,2),
ADD COLUMN sample_code_metadata JSONB DEFAULT '{}',

ADD COLUMN investigation TEXT,
ADD COLUMN investigation_confidence DECIMAL(3,2),
ADD COLUMN investigation_metadata JSONB DEFAULT '{}',

ADD COLUMN patient_name_th_encrypted BYTEA,
ADD COLUMN patient_name_th_confidence DECIMAL(3,2),
ADD COLUMN patient_name_th_metadata JSONB DEFAULT '{}',

ADD COLUMN patient_name_en_encrypted BYTEA,
ADD COLUMN patient_name_en_confidence DECIMAL(3,2),
ADD COLUMN patient_name_en_metadata JSONB DEFAULT '{}',

ADD COLUMN dob_gregorian_encrypted BYTEA,
ADD COLUMN dob_gregorian_confidence DECIMAL(3,2),
ADD COLUMN dob_gregorian_metadata JSONB DEFAULT '{}',

ADD COLUMN dob_buddhist_era_encrypted BYTEA,
ADD COLUMN dob_buddhist_era_confidence DECIMAL(3,2),
ADD COLUMN dob_buddhist_era_metadata JSONB DEFAULT '{}',

ADD COLUMN patient_contact_no_encrypted BYTEA,
ADD COLUMN patient_contact_no_confidence DECIMAL(3,2),
ADD COLUMN patient_contact_no_metadata JSONB DEFAULT '{}',

ADD COLUMN referring_physician_th_encrypted BYTEA,
ADD COLUMN referring_physician_th_confidence DECIMAL(3,2),
ADD COLUMN referring_physician_th_metadata JSONB DEFAULT '{}',

ADD COLUMN referring_physician_en_encrypted BYTEA,
ADD COLUMN referring_physician_en_confidence DECIMAL(3,2),
ADD COLUMN referring_physician_en_metadata JSONB DEFAULT '{}',

ADD COLUMN referring_physician_md_code TEXT,
ADD COLUMN referring_physician_md_code_confidence DECIMAL(3,2),
ADD COLUMN referring_physician_md_code_metadata JSONB DEFAULT '{}',

ADD COLUMN referring_physician_email_array_encrypted BYTEA,
ADD COLUMN referring_physician_email_array_confidence DECIMAL(3,2),
ADD COLUMN referring_physician_email_array_metadata JSONB DEFAULT '{}';

-- =====================================================
-- STEP 2: Add Field Validation Constraints
-- =====================================================

-- Add constraints for new confidence fields
ALTER TABLE medical_records_extraction
ADD CONSTRAINT extraction_new_field_confidence_range CHECK (
    (patient_code_confidence IS NULL OR (patient_code_confidence >= 0 AND patient_code_confidence <= 1)) AND
    (sample_code_confidence IS NULL OR (sample_code_confidence >= 0 AND sample_code_confidence <= 1)) AND
    (investigation_confidence IS NULL OR (investigation_confidence >= 0 AND investigation_confidence <= 1)) AND
    (patient_name_th_confidence IS NULL OR (patient_name_th_confidence >= 0 AND patient_name_th_confidence <= 1)) AND
    (patient_name_en_confidence IS NULL OR (patient_name_en_confidence >= 0 AND patient_name_en_confidence <= 1)) AND
    (dob_gregorian_confidence IS NULL OR (dob_gregorian_confidence >= 0 AND dob_gregorian_confidence <= 1)) AND
    (dob_buddhist_era_confidence IS NULL OR (dob_buddhist_era_confidence >= 0 AND dob_buddhist_era_confidence <= 1)) AND
    (patient_contact_no_confidence IS NULL OR (patient_contact_no_confidence >= 0 AND patient_contact_no_confidence <= 1)) AND
    (referring_physician_th_confidence IS NULL OR (referring_physician_th_confidence >= 0 AND referring_physician_th_confidence <= 1)) AND
    (referring_physician_en_confidence IS NULL OR (referring_physician_en_confidence >= 0 AND referring_physician_en_confidence <= 1)) AND
    (referring_physician_md_code_confidence IS NULL OR (referring_physician_md_code_confidence >= 0 AND referring_physician_md_code_confidence <= 1)) AND
    (referring_physician_email_array_confidence IS NULL OR (referring_physician_email_array_confidence >= 0 AND referring_physician_email_array_confidence <= 1))
);

-- =====================================================
-- STEP 3: Update Search Vector Function
-- =====================================================

-- Update search vector function to include new generic fields
CREATE OR REPLACE FUNCTION update_extraction_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := 
        -- High priority: patient identification
        setweight(to_tsvector('simple', COALESCE(NEW.patient_code, '')), 'A') ||
        setweight(to_tsvector('simple', COALESCE(NEW.sample_code, '')), 'A') ||
        setweight(to_tsvector('simple', COALESCE(NEW.investigation, '')), 'A') ||
        
        -- Medium priority: facility and physician info
        setweight(to_tsvector('simple', COALESCE(NEW.place_of_treatment, '')), 'B') ||
        setweight(to_tsvector('simple', COALESCE(NEW.referring_physician_md_code, '')), 'B') ||
        
        -- Legacy fields for backward compatibility
        setweight(to_tsvector('simple', COALESCE(NEW.hospital_id, '')), 'B') ||
        
        -- Lower priority: medical content
        setweight(to_tsvector('simple', COALESCE(NEW.diagnoses::text, '')), 'C') ||
        setweight(to_tsvector('simple', COALESCE(NEW.medications::text, '')), 'C') ||
        setweight(to_tsvector('simple', COALESCE(NEW.age, '')), 'D') ||
        setweight(to_tsvector('simple', COALESCE(NEW.sex, '')), 'D');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 4: Update Overall Confidence Calculation Function
-- =====================================================

-- Update confidence calculation to include new fields
CREATE OR REPLACE FUNCTION calculate_overall_confidence(
    p_extraction_id UUID
) RETURNS DECIMAL(3,2) AS $$
DECLARE
    v_confidence_scores DECIMAL[];
    v_overall_score DECIMAL(3,2);
BEGIN
    -- Collect all non-null confidence scores from both legacy and new fields
    SELECT ARRAY_AGG(confidence) INTO v_confidence_scores
    FROM (
        -- Legacy fields
        SELECT patient_name_confidence AS confidence FROM medical_records_extraction WHERE id = p_extraction_id AND patient_name_confidence IS NOT NULL
        UNION ALL
        SELECT thai_id_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND thai_id_confidence IS NOT NULL
        UNION ALL
        SELECT hospital_id_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND hospital_id_confidence IS NOT NULL
        UNION ALL
        SELECT hospital_number_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND hospital_number_confidence IS NOT NULL
        UNION ALL
        SELECT lab_number_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND lab_number_confidence IS NOT NULL
        UNION ALL
        SELECT age_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND age_confidence IS NOT NULL
        UNION ALL
        SELECT date_of_birth_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND date_of_birth_confidence IS NOT NULL
        UNION ALL
        SELECT sex_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND sex_confidence IS NOT NULL
        UNION ALL
        SELECT phone_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND phone_confidence IS NOT NULL
        UNION ALL
        -- New generic schema fields
        SELECT patient_code_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND patient_code_confidence IS NOT NULL
        UNION ALL
        SELECT sample_code_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND sample_code_confidence IS NOT NULL
        UNION ALL
        SELECT investigation_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND investigation_confidence IS NOT NULL
        UNION ALL
        SELECT patient_name_th_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND patient_name_th_confidence IS NOT NULL
        UNION ALL
        SELECT patient_name_en_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND patient_name_en_confidence IS NOT NULL
        UNION ALL
        SELECT dob_gregorian_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND dob_gregorian_confidence IS NOT NULL
        UNION ALL
        SELECT dob_buddhist_era_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND dob_buddhist_era_confidence IS NOT NULL
        UNION ALL
        SELECT patient_contact_no_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND patient_contact_no_confidence IS NOT NULL
        UNION ALL
        SELECT referring_physician_th_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND referring_physician_th_confidence IS NOT NULL
        UNION ALL
        SELECT referring_physician_en_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND referring_physician_en_confidence IS NOT NULL
        UNION ALL
        SELECT referring_physician_md_code_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND referring_physician_md_code_confidence IS NOT NULL
        UNION ALL
        SELECT referring_physician_email_array_confidence FROM medical_records_extraction WHERE id = p_extraction_id AND referring_physician_email_array_confidence IS NOT NULL
    ) AS scores;
    
    -- Calculate weighted average
    IF array_length(v_confidence_scores, 1) > 0 THEN
        SELECT AVG(score) INTO v_overall_score FROM unnest(v_confidence_scores) AS score;
    ELSE
        v_overall_score := 0.0;
    END IF;
    
    RETURN v_overall_score;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 5: Update Extraction Version Creation Function
-- =====================================================

-- Update the create_extraction_version function to include new fields
CREATE OR REPLACE FUNCTION create_extraction_version(
    p_document_id UUID,
    p_user_id UUID
) RETURNS UUID AS $$
DECLARE
    v_current_extraction medical_records_extraction%ROWTYPE;
    v_new_id UUID;
BEGIN
    -- Get current extraction
    SELECT * INTO v_current_extraction
    FROM medical_records_extraction
    WHERE document_id = p_document_id
        AND is_current = true
    LIMIT 1;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No current extraction found for document %', p_document_id;
    END IF;
    
    -- Mark current as not current
    UPDATE medical_records_extraction
    SET is_current = false,
        updated_at = NOW()
    WHERE id = v_current_extraction.id;
    
    -- Create new version
    v_new_id := uuid_generate_v4();
    
    INSERT INTO medical_records_extraction (
        id,
        document_id,
        organization_id,
        transaction_id,
        version,
        is_current,
        parent_version_id,
        -- Legacy fields
        patient_name_encrypted,
        patient_name_confidence,
        patient_name_metadata,
        thai_id_encrypted,
        thai_id_confidence,
        thai_id_metadata,
        hospital_id,
        hospital_id_confidence,
        hospital_id_metadata,
        hospital_number_encrypted,
        hospital_number_confidence,
        hospital_number_metadata,
        lab_number_encrypted,
        lab_number_confidence,
        lab_number_metadata,
        age,
        age_confidence,
        date_of_birth_encrypted,
        date_of_birth_confidence,
        sex,
        sex_confidence,
        phone_encrypted,
        phone_confidence,
        place_of_treatment,
        place_of_treatment_confidence,
        referring_physician_encrypted,
        referring_physician_confidence,
        referring_physician_email_encrypted,
        referring_physician_email_confidence,
        blood_collection_dates,
        blood_collection_dates_confidence,
        surgery_biopsy_dates,
        surgery_biopsy_dates_confidence,
        diagnoses,
        diagnoses_confidence,
        medications,
        medications_confidence,
        test_results,
        test_results_confidence,
        overall_confidence_score,
        extracted_text_full,
        detected_languages,
        extraction_warnings,
        cross_reference_notes,
        contextual_clues_used,
        manual_review_required,
        search_vector,
        -- New generic schema fields
        patient_code,
        patient_code_confidence,
        patient_code_metadata,
        sample_code,
        sample_code_confidence,
        sample_code_metadata,
        investigation,
        investigation_confidence,
        investigation_metadata,
        patient_name_th_encrypted,
        patient_name_th_confidence,
        patient_name_th_metadata,
        patient_name_en_encrypted,
        patient_name_en_confidence,
        patient_name_en_metadata,
        dob_gregorian_encrypted,
        dob_gregorian_confidence,
        dob_gregorian_metadata,
        dob_buddhist_era_encrypted,
        dob_buddhist_era_confidence,
        dob_buddhist_era_metadata,
        patient_contact_no_encrypted,
        patient_contact_no_confidence,
        patient_contact_no_metadata,
        referring_physician_th_encrypted,
        referring_physician_th_confidence,
        referring_physician_th_metadata,
        referring_physician_en_encrypted,
        referring_physician_en_confidence,
        referring_physician_en_metadata,
        referring_physician_md_code,
        referring_physician_md_code_confidence,
        referring_physician_md_code_metadata,
        referring_physician_email_array_encrypted,
        referring_physician_email_array_confidence,
        referring_physician_email_array_metadata
    )
    SELECT
        v_new_id,
        document_id,
        organization_id,
        transaction_id,
        version + 1,
        true,
        id,
        -- Copy all legacy fields
        patient_name_encrypted,
        patient_name_confidence,
        patient_name_metadata,
        thai_id_encrypted,
        thai_id_confidence,
        thai_id_metadata,
        hospital_id,
        hospital_id_confidence,
        hospital_id_metadata,
        hospital_number_encrypted,
        hospital_number_confidence,
        hospital_number_metadata,
        lab_number_encrypted,
        lab_number_confidence,
        lab_number_metadata,
        age,
        age_confidence,
        date_of_birth_encrypted,
        date_of_birth_confidence,
        sex,
        sex_confidence,
        phone_encrypted,
        phone_confidence,
        place_of_treatment,
        place_of_treatment_confidence,
        referring_physician_encrypted,
        referring_physician_confidence,
        referring_physician_email_encrypted,
        referring_physician_email_confidence,
        blood_collection_dates,
        blood_collection_dates_confidence,
        surgery_biopsy_dates,
        surgery_biopsy_dates_confidence,
        diagnoses,
        diagnoses_confidence,
        medications,
        medications_confidence,
        test_results,
        test_results_confidence,
        overall_confidence_score,
        extracted_text_full,
        detected_languages,
        extraction_warnings,
        cross_reference_notes,
        contextual_clues_used,
        manual_review_required,
        search_vector,
        -- Copy all new generic fields
        patient_code,
        patient_code_confidence,
        patient_code_metadata,
        sample_code,
        sample_code_confidence,
        sample_code_metadata,
        investigation,
        investigation_confidence,
        investigation_metadata,
        patient_name_th_encrypted,
        patient_name_th_confidence,
        patient_name_th_metadata,
        patient_name_en_encrypted,
        patient_name_en_confidence,
        patient_name_en_metadata,
        dob_gregorian_encrypted,
        dob_gregorian_confidence,
        dob_gregorian_metadata,
        dob_buddhist_era_encrypted,
        dob_buddhist_era_confidence,
        dob_buddhist_era_metadata,
        patient_contact_no_encrypted,
        patient_contact_no_confidence,
        patient_contact_no_metadata,
        referring_physician_th_encrypted,
        referring_physician_th_confidence,
        referring_physician_th_metadata,
        referring_physician_en_encrypted,
        referring_physician_en_confidence,
        referring_physician_en_metadata,
        referring_physician_md_code,
        referring_physician_md_code_confidence,
        referring_physician_md_code_metadata,
        referring_physician_email_array_encrypted,
        referring_physician_email_array_confidence,
        referring_physician_email_array_metadata
    FROM medical_records_extraction
    WHERE id = v_current_extraction.id;
    
    -- Log audit event
    PERFORM log_audit_event(
        'create'::audit_action,
        'medical_records_extraction',
        v_new_id,
        to_jsonb(v_current_extraction),
        NULL
    );
    
    RETURN v_new_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 6: Create Updated Views
-- =====================================================

-- Update current extractions view to include new fields
CREATE OR REPLACE VIEW current_medical_extractions AS
SELECT 
    e.*,
    d.file_name,
    d.status as document_status,
    d.uploaded_by,
    d.created_at as document_created_at,
    -- Add field completeness metrics
    (CASE WHEN e.patient_code IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.sample_code IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.investigation IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.patient_name_th_encrypted IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.patient_name_en_encrypted IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.dob_gregorian_encrypted IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.dob_buddhist_era_encrypted IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.patient_contact_no_encrypted IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.place_of_treatment IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.referring_physician_th_encrypted IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.referring_physician_en_encrypted IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.referring_physician_md_code IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN e.referring_physician_email_array_encrypted IS NOT NULL THEN 1 ELSE 0 END
    ) as extracted_fields_count,
    ROUND((
        (CASE WHEN e.patient_code IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.sample_code IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.investigation IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.patient_name_th_encrypted IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.patient_name_en_encrypted IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.dob_gregorian_encrypted IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.dob_buddhist_era_encrypted IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.patient_contact_no_encrypted IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.place_of_treatment IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.referring_physician_th_encrypted IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.referring_physician_en_encrypted IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.referring_physician_md_code IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN e.referring_physician_email_array_encrypted IS NOT NULL THEN 1 ELSE 0 END
        ) / 13.0 * 100
    ), 2) as extraction_completeness_percentage
FROM medical_records_extraction e
JOIN documents d ON e.document_id = d.id
WHERE e.is_current = true
    AND d.deleted_at IS NULL;

-- =====================================================
-- STEP 7: Create Indexes for New Fields
-- =====================================================

-- Create indexes for new searchable fields
CREATE INDEX idx_extraction_patient_code ON medical_records_extraction(patient_code) WHERE patient_code IS NOT NULL;
CREATE INDEX idx_extraction_sample_code ON medical_records_extraction(sample_code) WHERE sample_code IS NOT NULL;
CREATE INDEX idx_extraction_investigation ON medical_records_extraction(investigation) WHERE investigation IS NOT NULL;
CREATE INDEX idx_extraction_md_code ON medical_records_extraction(referring_physician_md_code) WHERE referring_physician_md_code IS NOT NULL;

-- Create partial indexes for confidence levels of new fields
CREATE INDEX idx_extraction_patient_code_confidence ON medical_records_extraction(patient_code_confidence) WHERE patient_code_confidence IS NOT NULL;
CREATE INDEX idx_extraction_investigation_confidence ON medical_records_extraction(investigation_confidence) WHERE investigation_confidence IS NOT NULL;

-- =====================================================
-- STEP 8: Update Memory with New Schema
-- =====================================================

-- Add comments for documentation
COMMENT ON COLUMN medical_records_extraction.patient_code IS 'Patient identifier beginning with TT (e.g., TT04035)';
COMMENT ON COLUMN medical_records_extraction.sample_code IS 'Random mix of 6 characters (English letters and digits)';
COMMENT ON COLUMN medical_records_extraction.investigation IS 'Test names (K-TRACK, SPOT-MAS, K4CARE, K-TRACK MET, etc.)';
COMMENT ON COLUMN medical_records_extraction.patient_name_th_encrypted IS 'Patient full name in Thai (encrypted)';
COMMENT ON COLUMN medical_records_extraction.patient_name_en_encrypted IS 'Patient full name in English (encrypted)';
COMMENT ON COLUMN medical_records_extraction.dob_gregorian_encrypted IS 'Date of birth YYYY-MM-DD Gregorian (encrypted)';
COMMENT ON COLUMN medical_records_extraction.dob_buddhist_era_encrypted IS 'Date of birth DD/MM/YYYY Buddhist Era +543 (encrypted)';
COMMENT ON COLUMN medical_records_extraction.patient_contact_no_encrypted IS 'Patient contact number (encrypted)';
COMMENT ON COLUMN medical_records_extraction.referring_physician_th_encrypted IS 'Referring physician name Thai (encrypted)';
COMMENT ON COLUMN medical_records_extraction.referring_physician_en_encrypted IS 'Referring physician name English (encrypted)';
COMMENT ON COLUMN medical_records_extraction.referring_physician_md_code IS 'Referring physician MD code';
COMMENT ON COLUMN medical_records_extraction.referring_physician_email_array_encrypted IS 'Array of referring physician emails (encrypted)';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if new columns were added
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'medical_records_extraction' 
  AND column_name LIKE '%patient_code%' 
  OR column_name LIKE '%sample_code%'
  OR column_name LIKE '%investigation%'
  OR column_name LIKE '%patient_name_th%'
  OR column_name LIKE '%patient_name_en%'
  OR column_name LIKE '%dob_gregorian%'
  OR column_name LIKE '%dob_buddhist_era%'
  OR column_name LIKE '%patient_contact_no%'
  OR column_name LIKE '%referring_physician_th%'
  OR column_name LIKE '%referring_physician_en%'
  OR column_name LIKE '%referring_physician_md_code%'
  OR column_name LIKE '%referring_physician_email_array%'
ORDER BY column_name;

-- Check new indexes
SELECT indexname 
FROM pg_indexes 
WHERE tablename = 'medical_records_extraction' 
  AND indexname LIKE '%patient_code%'
  OR indexname LIKE '%sample_code%'
  OR indexname LIKE '%investigation%'
  OR indexname LIKE '%md_code%'
ORDER BY indexname;

-- Test updated view
SELECT extracted_fields_count, extraction_completeness_percentage
FROM current_medical_extractions
LIMIT 1;