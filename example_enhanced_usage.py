#!/usr/bin/env python3
"""
Enhanced ChromoForge OCR Pipeline Usage Examples
Demonstrates the new Gemini 2.5 Pro integration with ULTRA THINK strategy.
"""

import asyncio
import logging
from pathlib import Path
from typing import List, Tuple
import uuid

from src.ocr_processor import GeminiOCRProcessor
from src.batch_processor import BatchProcessor
from src.config import settings


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_single_pdf_processing():
    """Example: Process a single PDF with enhanced OCR."""
    print("🔥 Enhanced Single PDF Processing Example")
    print("=" * 50)
    
    # Initialize enhanced OCR processor
    user_id = str(uuid.uuid4())
    session_id = str(uuid.uuid4())
    
    processor = GeminiOCRProcessor(
        user_id=user_id,
        session_id=session_id
    )
    
    # Process a sample PDF (replace with actual path)
    pdf_path = Path("original-pdf-examples/CSF TT04035.pdf")
    
    if pdf_path.exists():
        print(f"📄 Processing: {pdf_path.name}")
        print("🤖 Using ULTRA THINK strategy for maximum accuracy...")
        
        # Process with enhanced OCR
        result = await processor.process_pdf_with_retry(pdf_path)
        
        print(f"✅ Processing completed in {result.processing_time:.2f}s")
        print(f"🎯 Confidence Score: {result.confidence_score:.2f}")
        print(f"🌍 Detected Languages: {', '.join(result.detected_languages)}")
        
        # Display enhanced extracted fields
        print("\n📋 Enhanced Thai Medical Data Extraction:")
        print(f"  Patient Name: {result.patient_name or 'Not found'}")
        print(f"  Thai ID (เลขที่บัตร): {result.thai_id or 'Not found'}")
        print(f"  Hospital ID (รพ.วชิรพยาบาล): {result.hospital_id or 'Not found'}")
        print(f"  Age (อายุ): {result.age or 'Not found'}")
        print(f"  Sex: {result.sex or 'Not found'}")
        print(f"  Phone: {result.phone or 'Not found'}")
        print(f"  Referring Physician: {result.referring_physician or 'Not found'}")
        print(f"  Physician Email: {result.referring_physician_email or 'Not found'}")
        
        if result.blood_collection_dates:
            print(f"  Blood Collection Dates: {', '.join(result.blood_collection_dates)}")
        
        if result.surgery_biopsy_dates:
            print(f"  Surgery/Biopsy Dates: {', '.join(result.surgery_biopsy_dates)}")
        
        if result.warnings:
            print(f"⚠️  Warnings: {', '.join(result.warnings)}")
        
        # Record transaction if database is available
        if hasattr(processor, 'record_processing_transaction'):
            transaction_id = await processor.record_processing_transaction(
                pdf_path, result
            )
            if transaction_id:
                print(f"💾 Transaction recorded: {transaction_id}")
    else:
        print(f"❌ Sample PDF not found: {pdf_path}")
        print("   Please place a Thai medical PDF in the original-pdf-examples directory")


async def example_batch_processing():
    """Example: Process multiple PDFs with enhanced batch processing."""
    print("\n🔥 Enhanced Batch Processing Example")
    print("=" * 50)
    
    # Find all PDFs in examples directory
    examples_dir = Path("original-pdf-examples")
    pdf_files = list(examples_dir.glob("*.pdf"))[:3]  # Process first 3 files
    
    if not pdf_files:
        print("❌ No PDF files found in original-pdf-examples directory")
        return
    
    print(f"📚 Processing {len(pdf_files)} files with enhanced batch processor...")
    
    # Initialize enhanced OCR processor
    processor = GeminiOCRProcessor(
        user_id=str(uuid.uuid4()),
        session_id=str(uuid.uuid4())
    )
    
    # Use enhanced batch processing with database recording
    if hasattr(processor, 'process_pdf_batch'):
        results = await processor.process_pdf_batch(pdf_files)
        
        print("\n📊 Batch Processing Results:")
        for pdf_path, ocr_result, transaction_id in results:
            print(f"  📄 {pdf_path.name}:")
            print(f"    ⏱️  Time: {ocr_result.processing_time:.2f}s")
            print(f"    🎯 Confidence: {ocr_result.confidence_score:.2f}")
            print(f"    💾 Transaction: {transaction_id or 'Not recorded'}")
            
            if ocr_result.errors:
                print(f"    ❌ Errors: {', '.join(ocr_result.errors)}")
    else:
        # Fallback to regular batch processing
        batch_processor = BatchProcessor(processor)
        output_dir = Path("output/batch_test")
        
        results = await batch_processor.process_batch(
            pdf_files, output_dir, enable_obfuscation=True
        )
        
        stats = results["batch_stats"]
        print(f"✅ Completed: {stats.completed_files}/{stats.total_files}")
        print(f"⏱️  Average time: {stats.average_processing_time:.2f}s")


async def example_multiple_file_upload():
    """Example: Simulate multiple file upload processing."""
    print("\n🔥 Multiple File Upload Simulation")
    print("=" * 50)
    
    # Simulate uploaded files (in real scenario, these come from web interface)
    examples_dir = Path("original-pdf-examples")
    pdf_files = list(examples_dir.glob("*.pdf"))[:2]  # Take first 2 files
    
    if not pdf_files:
        print("❌ No PDF files found for upload simulation")
        return
    
    # Read file contents (simulating upload)
    uploaded_files: List[Tuple[Path, bytes]] = []
    for pdf_path in pdf_files:
        with open(pdf_path, 'rb') as f:
            content = f.read()
        uploaded_files.append((pdf_path, content))
    
    print(f"📤 Simulating upload of {len(uploaded_files)} files...")
    
    # Initialize batch processor with enhanced OCR
    processor = GeminiOCRProcessor(
        user_id=str(uuid.uuid4()),
        session_id=str(uuid.uuid4())
    )
    batch_processor = BatchProcessor(processor)
    
    # Process uploads with async processing
    if hasattr(batch_processor, 'process_multiple_uploads'):
        output_dir = Path("output/uploads_test")
        
        result = await batch_processor.process_multiple_uploads(
            uploaded_files,
            output_dir,
            organization_id=str(uuid.uuid4()),
            async_processing=True  # Enable async processing
        )
        
        print("📋 Upload Processing Result:")
        print(f"  🆔 Job ID: {result['job_id']}")
        print(f"  📊 Status: {result['status']}")
        print(f"  📁 Total Files: {result['total_files']}")
        print(f"  ⏱️  Estimated Time: {result['estimated_completion_time']}s")
        print(f"  💬 Message: {result['message']}")
    else:
        print("❌ Multiple upload processing not available")


async def example_configuration_demo():
    """Example: Demonstrate enhanced configuration options."""
    print("\n🔥 Enhanced Configuration Demo")
    print("=" * 50)
    
    from src.config import get_gemini_config, get_database_config
    
    # Display enhanced Gemini configuration
    gemini_config = get_gemini_config()
    print("🤖 Enhanced Gemini Configuration:")
    print(f"  Model: {gemini_config['model']}")
    print(f"  Temperature: {gemini_config['temperature']}")
    print(f"  Max Tokens: {gemini_config['max_tokens']}")
    print(f"  Thinking Budget: {gemini_config['thinking_budget']} (unlimited)")
    print(f"  ULTRA THINK: {'Enabled' if gemini_config['enable_ultra_think'] else 'Disabled'}")
    print(f"  Google Search: {'Enabled' if gemini_config['enable_google_search'] else 'Disabled'}")
    print(f"  URL Context: {'Enabled' if gemini_config['enable_url_context'] else 'Disabled'}")
    print(f"  Thai Cross-Reference: {'Enabled' if gemini_config['thai_cross_reference'] else 'Disabled'}")
    print(f"  Contextual Name Mapping: {'Enabled' if gemini_config['contextual_name_mapping'] else 'Disabled'}")
    
    # Display database configuration
    try:
        db_config = get_database_config()
        print("\n💾 Database Configuration:")
        print(f"  Recording: {'Enabled' if db_config['enable_recording'] else 'Disabled'}")
        print(f"  Supabase URL: {db_config['supabase_url'][:30]}..." if db_config['supabase_url'] else "  Supabase URL: Not configured")
        print(f"  Default Org ID: {db_config['default_organization_id'] or 'Auto-generated'}")
    except Exception as e:
        print(f"⚠️  Database configuration: {e}")


async def main():
    """Run all examples."""
    print("🚀 ChromoForge Enhanced OCR Pipeline Examples")
    print("=" * 60)
    print("Using Gemini 2.5 Pro with ULTRA THINK strategy")
    print("For 100% accuracy with Thai handwritten medical documents")
    print("=" * 60)
    
    try:
        # Run configuration demo first
        await example_configuration_demo()
        
        # Run processing examples
        await example_single_pdf_processing()
        await example_batch_processing()
        await example_multiple_file_upload()
        
        print("\n✅ All examples completed successfully!")
        print("\n📚 Key Features Demonstrated:")
        print("  • ULTRA THINK mode for maximum accuracy")
        print("  • Cross-referencing handwritten vs printed text")
        print("  • Contextual clues for Thai name mapping")
        print("  • Enhanced Thai medical field extraction")
        print("  • Database transaction recording")
        print("  • Async processing for multiple uploads")
        print("  • Unlimited thinking budget (-1)")
        print("  • Google Search and URL context tools")
        
    except Exception as e:
        logger.error(f"Example execution failed: {e}")
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())