# Architecture Decision Records (ADRs)

**Document ID**: ADR-2025-01-04-001  
**Version**: 1.0  
**Date**: January 4, 2025  
**Project**: ChromoForge OCR Pipeline  

## ADR-001: Gemini 2.5 Pro API Selection for OCR Processing

**Status**: Accepted  
**Date**: 2025-01-04  
**Deciders**: Technical Architecture Team  

### Context

We need an OCR service capable of processing medical PDFs containing mixed Thai and English text, including handwritten notes. The service must handle direct PDF processing without requiring image conversion and provide structured data extraction capabilities.

### Decision

We will use Google Gemini 2.5 Pro API as our primary OCR service.

### Rationale

**Advantages of Gemini 2.5 Pro**:
- **Superior Multilingual Support**: Native Thai and English processing without additional preprocessing
- **Direct PDF Processing**: Accepts PDF files directly via Base64 encoding, eliminating conversion steps
- **Structured Output**: Can return JSON-formatted responses with medical data extraction
- **Handwritten Text Support**: Better performance on handwritten medical notes
- **Context Understanding**: AI-powered interpretation of medical terminology and context
- **Cost Effectiveness**: Competitive pricing compared to specialized medical OCR services

### Consequences

**Positive**:
- Single API call for complete PDF processing
- Reduced system complexity (no image conversion pipeline)
- Better accuracy for Thai medical terminology
- Structured JSON responses reduce parsing complexity
- Built-in confidence scoring and error handling

**Negative**:
- **Vendor Lock-in**: Dependency on Google's API availability and pricing
- **Rate Limits**: 60 requests/minute, 1,000/day on free tier
- **Token Limits**: 32,000 tokens per request may limit large document processing
- **Network Dependency**: Requires stable internet connection
- **Data Privacy**: Medical data sent to third-party service (mitigated by Google's HIPAA compliance)

**Mitigations**:
- Implement comprehensive retry logic with exponential backoff
- Monitor API quotas and implement queue management
- Establish fallback processing pipeline for critical documents
- Negotiate enterprise contract for higher limits and SLA guarantees
- Implement request batching and optimization strategies

---

## ADR-002: Coordinate-Based PII Obfuscation Strategy

**Status**: Accepted  
**Date**: 2025-01-04  
**Deciders**: Security Team, Technical Architecture Team  

### Context

We need a method to remove or obfuscate PII from processed PDFs while maintaining document readability and structure. The solution must provide precise PII removal without affecting non-sensitive content.

### Decision

We will implement coordinate-based PII obfuscation using PyMuPDF with text coordinate mapping.

### Rationale

**Coordinate-Based Approach Advantages**:
- **Precision**: Exact PII location targeting without affecting surrounding text
- **Document Preservation**: Maintains original layout, fonts, and formatting
- **Selective Obfuscation**: Can apply different methods per PII type
- **Audit Trail**: Exact coordinates logged for compliance verification
- **Reversibility**: Coordinates enable re-processing if needed
- **Multiple Methods**: Support for redaction, black boxes, blurring, etc.

### Consequences

**Positive**:
- Surgical PII removal preserves document utility
- Multiple obfuscation methods available (black box, redaction, blur)
- Detailed audit trail for compliance verification
- Before/after validation capabilities
- Support for complex document layouts

**Negative**:
- **Complex Implementation**: Requires precise text-to-coordinate mapping
- **PDF Format Dependency**: Limited to PDF documents only
- **Coordinate Accuracy**: Mapping errors may leave PII exposed or over-redact
- **Performance Impact**: Additional processing step increases latency
- **Library Dependency**: Relies on PyMuPDF for coordinate manipulation

---

## ADR-003: AsyncIO Concurrency Model

**Status**: Accepted  
**Date**: 2025-01-04  
**Deciders**: Performance Team, Technical Architecture Team  

### Context

We need a concurrency model that can handle multiple PDF processing operations while respecting API rate limits, managing system resources, and providing responsive user experience.

### Decision

We will use Python AsyncIO with semaphore-based rate limiting for concurrent processing.

### Rationale

**AsyncIO Advantages**:
- **Efficient I/O Handling**: Non-blocking operations for API calls and file I/O
- **Resource Efficiency**: Single-threaded execution with cooperative multitasking
- **Rate Limit Compliance**: Easy semaphore implementation for API quotas
- **Scalability**: Can handle hundreds of concurrent operations
- **Error Isolation**: Individual operation failures don't affect others
- **Memory Efficiency**: Lower memory overhead than threading/multiprocessing

### Consequences

**Positive**:
- Excellent performance for I/O-bound operations (API calls, file operations)
- Built-in rate limiting with asyncio.Semaphore
- Responsive progress reporting and cancellation support
- Memory-efficient handling of large batch operations
- Natural integration with async HTTP clients

**Negative**:
- **Learning Curve**: AsyncIO patterns require careful implementation
- **Debugging Complexity**: Async stack traces can be difficult to follow
- **CPU-Bound Limitations**: Not optimal for CPU-intensive operations
- **Third-Party Support**: Some libraries may not support async operations
- **Context Switching**: Improper async usage can degrade performance

---

**Next Review**: January 18, 2025  
**Review Criteria**: Technical metrics, cost analysis, security posture  
**Decision Authority**: Technical Architecture Review Board