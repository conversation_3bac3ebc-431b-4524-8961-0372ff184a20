# ChromoForge OCR Pipeline - Technical Specification

**Document ID**: SPEC-2025-01-04-001  
**Version**: 1.0  
**Date**: January 4, 2025  
**Status**: Draft  
**Author**: ChromoForge Team  
**Project**: ChromoForge OCR Pipeline  

## Executive Summary

ChromoForge OCR Pipeline is a comprehensive medical document processing system designed to extract structured data from Thai medical PDFs while maintaining HIPAA compliance and data security. The system uses Google Gemini 2.5 Pro API for OCR processing, implements coordinate-based PII obfuscation, and integrates with Supabase for secure data persistence.

## System Architecture

### Component Overview

```mermaid
graph TB
    A[PDF Input] --> B[OCR Processor]
    B --> C[PII Detector]
    C --> D[PDF Obfuscator]
    D --> E[Supabase Storage]
    
    B --> F[Gemini 2.5 Pro API]
    C --> G[Thai/English Regex Engine]
    D --> H[Coordinate Mapper]
    E --> I[Encrypted Database]
```

### Core Components

1. **OCR Processor** (`src/ocr_processor.py`)
   - Integrates with Gemini 2.5 Pro API for direct PDF processing
   - Handles mixed Thai/English content with handwritten text support
   - Implements retry logic with exponential backoff
   - Returns structured medical data with confidence scoring

2. **PII Detector** (`src/pii_detector.py`)
   - Comprehensive regex patterns for Thai and English PII
   - Supports Thai National ID, hospital numbers, lab numbers
   - Implements confidence scoring algorithms
   - Context-aware PII classification

3. **PDF Obfuscator** (`src/pdf_obfuscator.py`)
   - Coordinate-based PII masking using PyMuPDF
   - Multiple obfuscation methods (black box, redaction, white box)
   - Preserves document structure and readability
   - Before/after validation capabilities

4. **Batch Processor** (`src/batch_processor.py`)
   - Concurrent processing with semaphore-based rate limiting
   - Comprehensive error handling and recovery
   - Progress tracking and performance monitoring
   - Real-time statistics and reporting

## Success Metrics

### Technical KPIs
- OCR Accuracy: > 90% for Thai medical documents
- Processing Speed: 95th percentile < 30 seconds per PDF
- System Uptime: > 99.5% availability
- Error Rate: < 1% of total processing operations
- API Success Rate: > 99% successful Gemini API calls

### Security KPIs
- PII Protection: 100% PII encrypted at rest
- Access Violations: 0 unauthorized data access incidents
- Audit Compliance: 100% PII access events logged
- Vulnerability Management: 0 high/critical vulnerabilities
- Data Breach Prevention: 0 PII exposure incidents

---

**Document Control**:
- Review Required: Technical Architecture Team
- Approval Required: Security Team, Compliance Team
- Next Review Date: January 18, 2025
- Version Control: Git repository with signed commits