# ChromoForge Setup Guide

## Prerequisites

✅ **Python 3.9+ installed**
✅ **Virtual environment created and activated**
✅ **Dependencies installed**

## Step 1: Get Google Gemini API Key

1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the API key

## Step 2: Set up Supabase Project

1. Go to [Supabase](https://supabase.com)
2. Sign up/Sign in
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - Name: `ChromoForge`
   - Database Password: (choose a strong password)
   - Region: Choose closest to you
6. Wait for project creation (2-3 minutes)

## Step 3: Get Supabase Configuration

Once your project is ready:

1. Go to **Settings** → **API**
2. Copy these values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)
   - **service_role secret key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)

## Step 4: Configure Environment Variables

Edit the `.env` file in your project root:

```bash
# Replace these with your actual values
GOOGLE_API_KEY=your-actual-google-api-key
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-actual-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-actual-service-role-key
```

## Step 5: Set up Supabase Database

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `supabase-setup.sql` and run it
3. Go to **Storage** and create these buckets:
   - `original-documents` (Private)
   - `obfuscated-documents` (Private)
4. Copy the contents of `storage-policies.sql` and run it in SQL Editor

## Step 6: Test the Setup

Run this command to test your configuration:

```bash
source venv/bin/activate
python -c "
import os
from dotenv import load_dotenv
load_dotenv()

print('✓ Environment loaded')
print(f'Google API Key: {\"✓ Set\" if os.getenv(\"GOOGLE_API_KEY\") else \"✗ Missing\"}')
print(f'Supabase URL: {\"✓ Set\" if os.getenv(\"NEXT_PUBLIC_SUPABASE_URL\") else \"✗ Missing\"}')
print(f'Supabase Anon Key: {\"✓ Set\" if os.getenv(\"NEXT_PUBLIC_SUPABASE_ANON_KEY\") else \"✗ Missing\"}')
print(f'Supabase Service Key: {\"✓ Set\" if os.getenv(\"SUPABASE_SERVICE_ROLE_KEY\") else \"✗ Missing\"}')
"
```

## Next Steps

Once all environment variables are set:

1. Run the OCR pipeline on sample files
2. Verify output and results
3. Test all components

## Troubleshooting

- **API Key Issues**: Make sure your Google API key has Gemini API access enabled
- **Supabase Connection**: Verify your project URL and keys are correct
- **Database Issues**: Make sure you've run the setup SQL scripts
- **Storage Issues**: Ensure storage buckets are created and policies applied
