# ChromoForge Implementation Guide

## Quick Start

### 1. Database Setup

1. **Create a new Supabase project** at [supabase.com](https://supabase.com)

2. **Run the setup script** in the SQL Editor:
   - Navigate to SQL Editor in your Supabase Dashboard
   - Copy and paste the contents of `supabase-setup.sql`
   - Execute the script

3. **Create storage buckets**:
   - Go to Storage in your Supabase Dashboard
   - Create bucket: `original-documents` (Private)
   - Create bucket: `obfuscated-documents` (Private)

4. **Apply storage policies**:
   - Copy and paste the contents of `storage-policies.sql`
   - Execute in SQL Editor

5. **Initialize encryption keys** for your organization:
   ```sql
   -- Replace with your organization ID
   SELECT create_test_encryption_key('your-org-id-here');
   ```

### 2. Environment Setup

Create a `.env.local` file:

```env
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 3. Authentication Configuration

Enable authentication providers in Supabase Dashboard:
- Email/Password (recommended for medical applications)
- Configure email templates for compliance
- Set up custom SMTP for production

### 4. Client SDK Setup

```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'
import { Database } from './database.types'

export const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)
```

## API Examples

### User Registration

```typescript
async function registerUser(email: string, password: string, role: 'viewer' | 'editor' | 'admin') {
  // 1. Create auth user
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        role,
        organization_id: 'your-org-id'
      }
    }
  })

  if (authError) throw authError

  return authData
}
```

### Document Upload

```typescript
async function uploadDocument(file: File) {
  // 1. Generate checksum
  const checksum = await generateSHA256(file)
  
  // 2. Check for duplicates
  const { data: existing } = await supabase
    .from('documents')
    .select('id')
    .eq('checksum', checksum)
    .single()
    
  if (existing) {
    throw new Error('Document already exists')
  }

  // 3. Upload to storage
  const filePath = `${orgId}/${Date.now()}-${file.name}`
  const { error: uploadError } = await supabase.storage
    .from('original-documents')
    .upload(filePath, file)
    
  if (uploadError) throw uploadError

  // 4. Create document record
  const { data: document, error: docError } = await supabase
    .from('documents')
    .insert({
      file_name: file.name,
      file_size: file.size,
      mime_type: file.type,
      checksum,
      original_path: filePath,
      status: 'processing'
    })
    .select()
    .single()
    
  return document
}
```

### Medical Record Creation with Encrypted PII

```typescript
async function createMedicalRecord(
  documentId: string,
  patientData: {
    name: string
    thaiId: string
    hospitalNumber: string
    labNumber: string
  },
  medicalData: any
) {
  // Server-side function to handle encryption
  const { data, error } = await supabase.rpc('create_medical_record', {
    p_document_id: documentId,
    p_patient_name: patientData.name,
    p_patient_id: patientData.thaiId,
    p_hospital_number: patientData.hospitalNumber,
    p_lab_number: patientData.labNumber,
    p_record_data: medicalData
  })
  
  return data
}
```

### Searching Medical Records

```typescript
async function searchMedicalRecords(query: string) {
  // Search non-PII fields only
  const { data, error } = await supabase
    .from('medical_records')
    .select(`
      id,
      record_date,
      record_type,
      department,
      diagnoses,
      document:documents(file_name)
    `)
    .textSearch('search_vector', query)
    .is('deleted_at', null)
    .order('record_date', { ascending: false })
    
  return data
}
```

### Role-Based Data Access

```typescript
async function getMedicalRecords() {
  // Use the secure view that handles PII decryption based on role
  const { data, error } = await supabase
    .from('medical_records_secure')
    .select('*')
    .order('created_at', { ascending: false })
    
  return data
}
```

## Security Best Practices

### 1. API Security

```typescript
// Always use RLS
supabase.auth.getSession() // Ensure user is authenticated

// Never expose service role key to client
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
)
```

### 2. File Upload Security

```typescript
// Validate files before upload
function validatePDF(file: File): boolean {
  const validTypes = ['application/pdf']
  const maxSize = 50 * 1024 * 1024 // 50MB
  
  if (!validTypes.includes(file.type)) {
    throw new Error('Only PDF files are allowed')
  }
  
  if (file.size > maxSize) {
    throw new Error('File size exceeds 50MB limit')
  }
  
  return true
}
```

### 3. PII Handling

```typescript
// Never decrypt PII on client side
// Always use server functions or secure views

// Bad
const decryptedName = decrypt(patient.name_encrypted) // ❌

// Good
const { patient_name } = await supabase
  .from('medical_records_secure')
  .select('patient_name')
  .single() // ✅
```

### 4. Audit Logging

```typescript
// Important actions should be logged
async function exportPatientData(patientId: string) {
  // Log the export action
  await supabase.rpc('log_audit_event', {
    p_action: 'export',
    p_table_name: 'medical_records',
    p_record_id: null,
    p_new_values: { patient_id: patientId }
  })
  
  // Perform the export
  const data = await supabase.rpc('export_user_data', {
    patient_id_to_export: patientId
  })
  
  return data
}
```

## Monitoring & Maintenance

### 1. Set Up Alerts

```sql
-- Create function to check for suspicious activity
CREATE OR REPLACE FUNCTION check_usage_alerts()
RETURNS void AS $$
DECLARE
  high_usage RECORD;
BEGIN
  -- Check for excessive API calls
  FOR high_usage IN 
    SELECT user_id, COUNT(*) as request_count
    FROM audit_logs
    WHERE created_at > NOW() - INTERVAL '1 hour'
    GROUP BY user_id
    HAVING COUNT(*) > 1000
  LOOP
    -- Send alert (integrate with your monitoring system)
    RAISE NOTICE 'High usage alert for user %', high_usage.user_id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;
```

### 2. Regular Maintenance Tasks

```sql
-- Weekly: Check for orphaned documents
SELECT d.* FROM documents d
LEFT JOIN medical_records m ON d.id = m.document_id
WHERE m.id IS NULL 
  AND d.created_at < NOW() - INTERVAL '1 day';

-- Monthly: Archive old audit logs
INSERT INTO audit_logs_archive 
SELECT * FROM audit_logs 
WHERE created_at < NOW() - INTERVAL '90 days';

-- Quarterly: Rotate encryption keys
SELECT rotate_encryption_keys('your-org-id');
```

### 3. Performance Monitoring

```sql
-- Monitor slow queries
SELECT 
  query,
  calls,
  mean_exec_time,
  total_exec_time
FROM pg_stat_statements
WHERE mean_exec_time > 1000 -- queries taking > 1 second
ORDER BY mean_exec_time DESC
LIMIT 20;

-- Check table sizes
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## Compliance Checklist

### HIPAA Compliance
- [ ] Encryption at rest (pgcrypto) ✓
- [ ] Encryption in transit (SSL/TLS) ✓
- [ ] Access controls (RLS) ✓
- [ ] Audit logging ✓
- [ ] Data backup and recovery
- [ ] Business Associate Agreement (BAA) with Supabase
- [ ] Regular security assessments
- [ ] Employee training documentation

### Data Protection
- [ ] Implement data retention policies
- [ ] Regular key rotation schedule
- [ ] Data export capabilities ✓
- [ ] Right to deletion (soft delete) ✓
- [ ] Consent tracking
- [ ] Data minimization practices

### Security Measures
- [ ] Regular penetration testing
- [ ] Vulnerability scanning
- [ ] Incident response plan
- [ ] Access review procedures
- [ ] Security awareness training

## Troubleshooting

### Common Issues

1. **RLS Policy Denials**
   ```sql
   -- Debug RLS policies
   SET ROLE authenticated;
   SET request.jwt.claim.sub = 'user-uuid';
   SELECT * FROM documents; -- Test query
   ```

2. **Encryption Key Issues**
   ```sql
   -- Check if organization has active key
   SELECT * FROM encryption_keys 
   WHERE organization_id = 'your-org-id' 
     AND is_active = true;
   ```

3. **Storage Access Issues**
   ```sql
   -- Verify storage policies
   SELECT * FROM pg_policies 
   WHERE schemaname = 'storage' 
     AND tablename = 'objects';
   ```

## Support Resources

- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL RLS Guide](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)
- [HIPAA Compliance Guide](https://www.hhs.gov/hipaa/for-professionals/security/index.html)
- [pgcrypto Documentation](https://www.postgresql.org/docs/current/pgcrypto.html)