-- ChromoForge Storage Policies Setup
-- Execute this after creating storage buckets in Supabase Dashboard

-- =====================================================
-- STORAGE BUCKET POLICIES
-- =====================================================

-- Note: First create these buckets in Supabase Dashboard:
-- 1. 'original-documents' - for original PDFs with PII
-- 2. 'obfuscated-documents' - for redacted PDFs

-- =====================================================
-- Original Documents Bucket Policies
-- =====================================================

-- Only editors and admins can upload original documents
CREATE POLICY "Editors can upload original documents"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'original-documents'
    AND has_role(auth.uid(), 'editor')
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );

-- Only editors and admins can view original documents
CREATE POLICY "Editors can view original documents"
  ON storage.objects FOR SELECT
  USING (
    bucket_id = 'original-documents'
    AND has_role(auth.uid(), 'editor')
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );

-- Only admins can delete original documents
CREATE POLICY "Admins can delete original documents"
  ON storage.objects FOR DELETE
  USING (
    bucket_id = 'original-documents'
    AND has_role(auth.uid(), 'admin')
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );

-- Only editors can update original documents
CREATE POLICY "Editors can update original documents"
  ON storage.objects FOR UPDATE
  USING (
    bucket_id = 'original-documents'
    AND has_role(auth.uid(), 'editor')
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );

-- =====================================================
-- Obfuscated Documents Bucket Policies
-- =====================================================

-- All authenticated users can view obfuscated documents in their org
CREATE POLICY "Users can view obfuscated documents"
  ON storage.objects FOR SELECT
  USING (
    bucket_id = 'obfuscated-documents'
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );

-- Only editors can upload obfuscated documents
CREATE POLICY "Editors can upload obfuscated documents"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'obfuscated-documents'
    AND has_role(auth.uid(), 'editor')
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );

-- Only editors can update obfuscated documents
CREATE POLICY "Editors can update obfuscated documents"
  ON storage.objects FOR UPDATE
  USING (
    bucket_id = 'obfuscated-documents'
    AND has_role(auth.uid(), 'editor')
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );

-- Only admins can delete obfuscated documents
CREATE POLICY "Admins can delete obfuscated documents"
  ON storage.objects FOR DELETE
  USING (
    bucket_id = 'obfuscated-documents'
    AND has_role(auth.uid(), 'admin')
    AND (storage.foldername(name))[1] = get_user_organization(auth.uid())::text
  );

-- =====================================================
-- Storage Helper Functions
-- =====================================================

-- Function to generate secure file paths
CREATE OR REPLACE FUNCTION generate_secure_file_path(
  org_id UUID,
  file_type TEXT,
  original_filename TEXT
)
RETURNS TEXT AS $$
DECLARE
  file_extension TEXT;
  secure_filename TEXT;
BEGIN
  -- Extract file extension
  file_extension := substring(original_filename from '\.([^.]+)$');
  
  -- Generate secure filename with UUID
  secure_filename := format(
    '%s/%s/%s-%s.%s',
    org_id,
    file_type,
    to_char(NOW(), 'YYYY-MM-DD'),
    uuid_generate_v4(),
    COALESCE(file_extension, 'pdf')
  );
  
  RETURN secure_filename;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate file upload
CREATE OR REPLACE FUNCTION validate_file_upload(
  p_bucket_id TEXT,
  p_file_size BIGINT,
  p_mime_type TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check file size (max 50MB)
  IF p_file_size > 52428800 THEN
    RAISE EXCEPTION 'File size exceeds maximum allowed (50MB)';
  END IF;
  
  -- Check mime type
  IF p_mime_type != 'application/pdf' THEN
    RAISE EXCEPTION 'Only PDF files are allowed';
  END IF;
  
  -- Additional validations can be added here
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get storage usage for organization
CREATE OR REPLACE FUNCTION get_organization_storage_usage(org_id UUID)
RETURNS TABLE (
  bucket_name TEXT,
  file_count BIGINT,
  total_size BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.bucket_id as bucket_name,
    COUNT(*) as file_count,
    SUM(s.metadata->>'size')::BIGINT as total_size
  FROM storage.objects s
  WHERE (storage.foldername(s.name))[1] = org_id::text
  GROUP BY s.bucket_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- Storage Security Functions
-- =====================================================

-- Function to generate presigned upload URL
CREATE OR REPLACE FUNCTION generate_upload_url(
  p_bucket TEXT,
  p_filename TEXT,
  p_expires_in INTEGER DEFAULT 3600
)
RETURNS TEXT AS $$
DECLARE
  org_id UUID;
  secure_path TEXT;
BEGIN
  -- Get user's organization
  org_id := get_user_organization(auth.uid());
  
  -- Check permissions
  IF p_bucket = 'original-documents' AND NOT has_role(auth.uid(), 'editor') THEN
    RAISE EXCEPTION 'Insufficient permissions to upload to original documents';
  END IF;
  
  -- Generate secure path
  secure_path := generate_secure_file_path(org_id, p_bucket, p_filename);
  
  -- Log the upload intent
  PERFORM log_audit_event(
    'create'::audit_action,
    'storage',
    NULL,
    NULL,
    jsonb_build_object(
      'bucket', p_bucket,
      'path', secure_path,
      'original_filename', p_filename
    )
  );
  
  -- Return the secure path (actual presigned URL generation 
  -- should be done in application code)
  RETURN secure_path;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- Verification Queries
-- =====================================================

-- Check storage policies
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE schemaname = 'storage'
  AND tablename = 'objects'
ORDER BY policyname;