---
type: "always_apply"
---

# Code Style Guide v2.0.0

## General Formatting

### Standards
- 2 spaces indentation (enforced by ESLint/Prettier)
- Line length: 100 characters max
- Trailing commas in multi-line structures
- Semicolons required

### Naming Conventions
- **Variables/Functions**: camelCase (`userData`, `calculateTotal`)
- **Components/Classes**: PascalCase (`UserProfile`, `AuthProvider`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_RETRIES`, `API_URL`)
- **Files**: 
  - Components: `UserProfile.tsx`
  - Utilities: `formatDate.ts`
  - Types: `user.types.ts`
  - Tests: `*.test.ts` or `*.spec.ts`

## TypeScript

### Type Requirements
```typescript
// Always explicit types - no 'any'
interface User {
  id: string;
  email: string;
  profile?: UserProfile; // Optional with ?
}

// Enums for fixed values
enum Status {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE'
}

// Export types from .types.ts files
export type { User, UserProfile };
```

### Function Types
```typescript
// Named functions with explicit return types
function calculateTax(amount: number, rate: number): number {
  return Math.round(amount * rate);
}

// Arrow functions for callbacks
const handleClick = (id: string): void => {
  // Implementation
};
```

## React/Next.js

### Component Pattern
```typescript
import { FC } from 'react';

interface Props {
  title: string;
  onClick?: () => void;
}

export const Component: FC<Props> = ({ title, onClick }) => (
  <div className="p-4" onClick={onClick}>
    {title}
  </div>
);
```

### Hooks Rules
- Custom hooks prefixed with `use`
- Dependencies arrays always explicit
- Extract complex logic to custom hooks

### File Structure
```
src/
├── app/          # Next.js app directory
├── components/   # Reusable components
├── lib/         # Utilities
├── hooks/       # Custom hooks
└── types/       # TypeScript definitions
```

## CSS/TailwindCSS

### Class Organization
```tsx
// Group by: layout → spacing → appearance → state
<div className="
  flex items-center
  p-4 md:p-6
  bg-white border rounded-lg
  hover:shadow-md transition-shadow
">
```

### Responsive Design
- Mobile-first approach
- Use breakpoint prefixes: `sm:`, `md:`, `lg:`
- Avoid arbitrary values - use design tokens

## API Routes

### Pattern for Next.js 13+
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const schema = z.object({
  email: z.string().email(),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const data = schema.parse(body);
    // Process
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request' },
      { status: 400 }
    );
  }
}
```

## Testing

### Unit Test Structure
```typescript
describe('Component', () => {
  it('should render correctly', () => {
    // Arrange
    const props = { title: 'Test' };
    
    // Act
    render(<Component {...props} />);
    
    // Assert
    expect(screen.getByText('Test')).toBeInTheDocument();
  });
});
```

## Imports

### Order (enforced by ESLint)
```typescript
// 1. React/Next
import { useState } from 'react';
import { useRouter } from 'next/navigation';

// 2. External packages
import { z } from 'zod';

// 3. Internal - absolute paths
import { Button } from '@/components/ui';

// 4. Internal - relative paths
import { helper } from './utils';

// 5. Types
import type { User } from '@/types';
```

## Performance

### Key Patterns
```typescript
// Memoization for expensive operations
const result = useMemo(() => expensiveCalc(data), [data]);

// Stable callbacks
const handleSubmit = useCallback((data: FormData) => {
  // Process
}, [dependency]);

// Dynamic imports for code splitting
const HeavyComponent = dynamic(() => import('./Heavy'));
```

## Comments

### JSDoc for Functions
```typescript
/**
 * Processes payment transaction
 * @param amount - Amount in cents
 * @returns Transaction ID
 * @throws {PaymentError} On processing failure
 */
```

### Inline Comments
```typescript
// TODO: Migrate to new API (v2.1.0)
// FIXME: Handle edge case for null values
// NOTE: Temporary workaround for Safari bug
```