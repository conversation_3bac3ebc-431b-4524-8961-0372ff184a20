---
type: "always_apply"
---

# Tech Stack v2.0.0

## Core Stack

### Framework & Language
- **Framework**: Next.js 15+
- **Language**: TypeScript 5.0+ (strict mode)
- **Runtime**: Node.js 22 LTS
- **Package Manager**: npm

### Frontend
- **Styling**: TailwindCSS 4.0+
- **Components**: shadcn/ui
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **State**: Zustand / React Context
- **Forms**: React Hook Form + Zod
- **Data Fetching**: TanStack Query

### Backend Services
- **Database**: Supabase (PostgreSQL 17+)
- **Authentication**: Supabase Auth
- **Real-time**: Supabase Realtime
- **File Storage**: Supabase Storage
- **API Validation**: Zod
- **Background Jobs**: Trigger.dev

## Development

### Environment
- **Container**: Docker Desktop
- **Hot Reload**: Built-in Next.js HMR
- **Environment Config**:
  - `WATCHPACK_POLLING=true` (Windows)
  - `CHOKIDAR_USEPOLLING=true`

### Testing
- **Unit Tests**: Vitest
- **Component Tests**: React Testing Library
- **E2E Tests**: Playwright MCP
- **Visual Tests**: Percy

### Code Quality
- **Linting**: ESLint 9+
- **Formatting**: Prettier 3+
- **Type Checking**: TypeScript strict
- **Git Hooks**: Husky + lint-staged
- **Commits**: Conventional Commits

## Infrastructure

### Deployment
- **Hosting**: Vercel
- **Database**: Supabase Cloud
- **Edge Functions**: Vercel Edge
- **CDN**: Vercel Edge Network

### CI/CD
- **Platform**: GitHub Actions
- **Deploy Triggers**: Push to main/staging
- **Build Steps**:
  1. Type check
  2. Lint
  3. Unit tests
  4. Build
  5. E2E tests
  6. Deploy

### Monitoring
- **Errors**: Sentry
- **Analytics**: Vercel Analytics
- **Logs**: Axiom
- **Uptime**: Better Uptime

## Security

### Application
- **Secrets**: Vercel Environment Variables
- **API Security**: Rate limiting (Upstash)
- **CORS**: Next.js middleware
- **Headers**: CSP configured

### Database
- **RLS**: Enabled by default
- **SSL**: Enforced
- **Backups**: Daily automated
- **PITR**: Enabled for > 4GB

## Additional Services

### External APIs
- **Email**: Resend
- **Payments**: Stripe
- **Search**: Algolia
- **AI/LLM**: OpenAI via Vercel AI SDK

### Performance
- **Images**: Next.js Image optimization
- **Fonts**: Next.js Font optimization
- **Caching**: ISR + Vercel Edge Cache
- **Session Cache**: Upstash Redis

## Environment Variables

### Required for Production
```bash
# Database
DATABASE_URL=
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# External Services
RESEND_API_KEY=
STRIPE_SECRET_KEY=
SENTRY_DSN=

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=
```

### Development Only
```bash
# Local Development
NEXT_PUBLIC_API_URL=http://localhost:3000
ANALYZE_BUNDLE=true
```