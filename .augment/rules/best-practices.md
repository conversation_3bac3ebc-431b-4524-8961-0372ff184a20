---
type: "always_apply"
---

# Development Best Practices v2.0.0

## Core Principles

### Zero Tolerance Policy
- **ZERO warnings, ZERO errors, ZERO mistakes** in production code
- Record every issue in TO-DO.md synchronized with <PERSON> Code
- Never skip or bypass any problem

### Code Quality
- Write production-ready code from the start
- No placeholders or to-dos in code
- No hardcoded variables - use environment configuration
- Keep solutions simple and readable
- Extract repeated logic (DRY principle)

## Docker Development

### Requirements
- Use **Docker Desktop** with auto-refresh/hot-reload
- Environment variables for hot reload:
  - `WATCHPACK_POLLING=true` (Windows)
  - `CHOKIDAR_USEPOLLING=true`
- Monitor Docker logs continuously during development
- Build cloud-service-friendly environments

## Testing Standards

### Unit Testing
- **Every function must have a unit test**
- Use real-world data and connections (NO mocks)
- Tests must complete within 2 minutes
- Test actual functionality, not just passing status

### E2E Testing  
- **Use Playwright MCP exclusively** for E2E testing
- Follow Playwright best practices:
  - Role-based locators
  - Auto-retrying assertions
  - No custom timeouts (use built-in features)
- Check browser logs for hidden issues

## Security Requirements

### Environment Management
- Never commit sensitive data to version control
- Document all environment variables
- Use `.env.local` for local development
- Validate `.gitignore` includes all env files

### Supabase Security
- Enable Row-Level Security (RLS) by default
- Use short-lived JWTs with refresh
- Never expose service role keys to frontend
- Implement network restrictions

## Version Control

### Branching Strategy
1. Start with `dev` branch (never `main`)
2. Create feature branches: `feature/description`
3. Test on `dev` before production merge
4. Document merge strategy to production

### Commit Standards
```
feat(scope): add new feature
fix(scope): resolve bug
docs: update documentation
test: add tests
chore: maintenance tasks
```

## To-Do Tracking Requirements

### Priority Levels
- **P0**: Deployment blockers, security issues
- **P1**: UX degradation, performance problems  
- **P2**: Technical debt, non-critical bugs
- **P3**: Nice-to-have improvements

### Required Format
```markdown
- [ ] **[TYPE-001]** Description
  - **Type**: Error/Warning/Bug/Security
  - **Location**: file:line
  - **Detected**: YYYY-MM-DD
  - **Status**: Not Started/In Progress/Done
  - **Target**: vX.Y.Z
```

### Synchronization
- Update TO-DO.md before each commit
- Review P0/P1 items daily
- Archive completed items per release

## Dependencies

### Selection Criteria
- Recent commits (< 6 months)
- Active issue resolution
- Strong documentation
- Semantic versioning
- Security audit history

## Documentation

### Standards
- Use Semantic Versioning (X.Y.Z)
- No vague terms ("enhanced", "improved")
- Keep version history
- Sync documentation with code changes

## Production Checklist

Before deployment:
1. ✓ Zero build warnings/errors
2. ✓ All P0/P1 issues resolved
3. ✓ Unit tests passing with real data
4. ✓ E2E tests passing with Playwright MCP
5. ✓ Environment variables configured
6. ✓ No sensitive data in codebase
7. ✓ Security standards implemented
8. ✓ TO-DO.md synchronized
9. ✓ Documentation versioned
10. ✓ Performance metrics verified