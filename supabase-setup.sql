-- ChromoForge Supabase Setup Script
-- Execute this script in your Supabase SQL editor to set up the complete database schema

-- =====================================================
-- STEP 1: Enable Required Extensions
-- =====================================================

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- =====================================================
-- STEP 2: Create Custom Types
-- =====================================================

-- User role enum
CREATE TYPE user_role AS ENUM ('admin', 'editor', 'viewer', 'analyst');

-- Document status enum
CREATE TYPE document_status AS ENUM ('processing', 'completed', 'failed', 'archived');

-- Audit action types
CREATE TYPE audit_action AS ENUM (
  'create', 'read', 'update', 'delete', 'soft_delete', 
  'restore', 'export', 'login', 'logout', 'permission_change'
);

-- =====================================================
-- STEP 3: Create Helper Function for Timestamps
-- =====================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 4: Create Core Tables
-- =====================================================

-- Organizations Table
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  settings JSONB DEFAULT '{}',
  
  CONSTRAINT organizations_name_unique UNIQUE (name)
);

CREATE INDEX idx_organizations_deleted_at ON organizations(deleted_at);

-- User Profiles Table
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id),
  role user_role NOT NULL DEFAULT 'viewer',
  full_name TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  last_login_at TIMESTAMPTZ,
  settings JSONB DEFAULT '{}',
  
  CONSTRAINT user_profiles_org_user_unique UNIQUE (organization_id, id)
);

CREATE INDEX idx_user_profiles_organization_id ON user_profiles(organization_id);
CREATE INDEX idx_user_profiles_role ON user_profiles(role);
CREATE INDEX idx_user_profiles_deleted_at ON user_profiles(deleted_at);

-- Documents Table
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  uploaded_by UUID NOT NULL REFERENCES user_profiles(id),
  
  file_name TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  mime_type TEXT NOT NULL,
  checksum TEXT NOT NULL,
  
  original_path TEXT,
  obfuscated_path TEXT,
  
  status document_status DEFAULT 'processing',
  processing_started_at TIMESTAMPTZ,
  processing_completed_at TIMESTAMPTZ,
  processing_error JSONB,
  
  page_count INTEGER,
  metadata JSONB DEFAULT '{}',
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  
  CONSTRAINT documents_checksum_unique UNIQUE (organization_id, checksum),
  CONSTRAINT documents_file_size_positive CHECK (file_size > 0)
);

CREATE INDEX idx_documents_organization_id ON documents(organization_id);
CREATE INDEX idx_documents_uploaded_by ON documents(uploaded_by);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_created_at ON documents(created_at DESC);
CREATE INDEX idx_documents_deleted_at ON documents(deleted_at);

-- Medical Records Table
CREATE TABLE medical_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES documents(id),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  
  patient_name_encrypted BYTEA,
  patient_id_encrypted BYTEA,
  hospital_number_encrypted BYTEA,
  lab_number_encrypted BYTEA,
  
  record_date DATE,
  record_type TEXT,
  department TEXT,
  
  test_results JSONB DEFAULT '{}',
  diagnoses JSONB DEFAULT '[]',
  medications JSONB DEFAULT '[]',
  
  search_vector tsvector,
  
  confidence_score DECIMAL(3,2),
  extraction_metadata JSONB DEFAULT '{}',
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  
  CONSTRAINT medical_records_confidence_range CHECK (confidence_score >= 0 AND confidence_score <= 1)
);

CREATE INDEX idx_medical_records_document_id ON medical_records(document_id);
CREATE INDEX idx_medical_records_organization_id ON medical_records(organization_id);
CREATE INDEX idx_medical_records_record_date ON medical_records(record_date);
CREATE INDEX idx_medical_records_search_vector ON medical_records USING gin(search_vector);
CREATE INDEX idx_medical_records_deleted_at ON medical_records(deleted_at);

-- Audit Logs Table
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  user_id UUID REFERENCES user_profiles(id),
  
  action audit_action NOT NULL,
  table_name TEXT NOT NULL,
  record_id UUID,
  
  old_values JSONB,
  new_values JSONB,
  
  ip_address INET,
  user_agent TEXT,
  request_id UUID,
  
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  
  CONSTRAINT audit_logs_values_check CHECK (
    (action IN ('create', 'read') AND old_values IS NULL) OR
    (action IN ('update') AND old_values IS NOT NULL) OR
    (action NOT IN ('create', 'read', 'update'))
  )
);

CREATE INDEX idx_audit_logs_organization_id ON audit_logs(organization_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_table_name ON audit_logs(table_name);
CREATE INDEX idx_audit_logs_record_id ON audit_logs(record_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at DESC);

-- Make audit_logs append-only
REVOKE UPDATE, DELETE ON audit_logs FROM PUBLIC;

-- Encryption Keys Table
CREATE TABLE encryption_keys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  key_name TEXT NOT NULL,
  encrypted_key BYTEA NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  rotated_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT true,
  
  CONSTRAINT encryption_keys_org_name_unique UNIQUE (organization_id, key_name)
);

CREATE INDEX idx_encryption_keys_organization_id ON encryption_keys(organization_id);
CREATE INDEX idx_encryption_keys_is_active ON encryption_keys(is_active);

-- =====================================================
-- STEP 5: Create Security Helper Functions
-- =====================================================

-- Get active encryption key
CREATE OR REPLACE FUNCTION get_encryption_key(org_id UUID)
RETURNS BYTEA AS $$
DECLARE
  key BYTEA;
BEGIN
  SELECT encrypted_key INTO key
  FROM encryption_keys
  WHERE organization_id = org_id
    AND is_active = true
    AND key_name = 'primary'
  LIMIT 1;
  
  IF key IS NULL THEN
    RAISE EXCEPTION 'No active encryption key found for organization';
  END IF;
  
  RETURN key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Encrypt PII data
CREATE OR REPLACE FUNCTION encrypt_pii(data TEXT, org_id UUID)
RETURNS BYTEA AS $$
DECLARE
  key BYTEA;
BEGIN
  key := get_encryption_key(org_id);
  RETURN pgp_sym_encrypt(data, key::TEXT);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Decrypt PII data
CREATE OR REPLACE FUNCTION decrypt_pii(encrypted_data BYTEA, org_id UUID)
RETURNS TEXT AS $$
DECLARE
  key BYTEA;
BEGIN
  key := get_encryption_key(org_id);
  RETURN pgp_sym_decrypt(encrypted_data, key::TEXT);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Get user role
CREATE OR REPLACE FUNCTION get_user_role(user_id UUID)
RETURNS user_role AS $$
  SELECT role 
  FROM user_profiles 
  WHERE id = user_id AND deleted_at IS NULL
$$ LANGUAGE sql SECURITY DEFINER;

-- Get user organization
CREATE OR REPLACE FUNCTION get_user_organization(user_id UUID)
RETURNS UUID AS $$
  SELECT organization_id 
  FROM user_profiles 
  WHERE id = user_id AND deleted_at IS NULL
$$ LANGUAGE sql SECURITY DEFINER;

-- Check if user has role
CREATE OR REPLACE FUNCTION has_role(user_id UUID, required_role user_role)
RETURNS BOOLEAN AS $$
DECLARE
  user_role_value user_role;
BEGIN
  user_role_value := get_user_role(user_id);
  
  CASE required_role
    WHEN 'admin' THEN
      RETURN user_role_value = 'admin';
    WHEN 'editor' THEN
      RETURN user_role_value IN ('admin', 'editor');
    WHEN 'viewer' THEN
      RETURN user_role_value IN ('admin', 'editor', 'viewer');
    WHEN 'analyst' THEN
      RETURN user_role_value IN ('admin', 'analyst');
    ELSE
      RETURN FALSE;
  END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 6: Create Audit Functions
-- =====================================================

-- Log audit events
CREATE OR REPLACE FUNCTION log_audit_event(
  p_action audit_action,
  p_table_name TEXT,
  p_record_id UUID,
  p_old_values JSONB DEFAULT NULL,
  p_new_values JSONB DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO audit_logs (
    organization_id,
    user_id,
    action,
    table_name,
    record_id,
    old_values,
    new_values,
    ip_address,
    user_agent,
    request_id
  )
  VALUES (
    get_user_organization(auth.uid()),
    auth.uid(),
    p_action,
    p_table_name,
    p_record_id,
    p_old_values,
    p_new_values,
    current_setting('request.headers', true)::json ->> 'x-forwarded-for',
    current_setting('request.headers', true)::json ->> 'user-agent',
    current_setting('request.headers', true)::json ->> 'x-request-id'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    PERFORM log_audit_event(
      'create'::audit_action,
      TG_TABLE_NAME,
      NEW.id,
      NULL,
      to_jsonb(NEW)
    );
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    PERFORM log_audit_event(
      'update'::audit_action,
      TG_TABLE_NAME,
      NEW.id,
      to_jsonb(OLD),
      to_jsonb(NEW)
    );
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    PERFORM log_audit_event(
      'delete'::audit_action,
      TG_TABLE_NAME,
      OLD.id,
      to_jsonb(OLD),
      NULL
    );
    RETURN OLD;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Soft delete function
CREATE OR REPLACE FUNCTION soft_delete(table_name TEXT, record_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  EXECUTE format('UPDATE %I SET deleted_at = NOW() WHERE id = $1 AND deleted_at IS NULL', table_name)
  USING record_id;
  
  PERFORM log_audit_event(
    'soft_delete'::audit_action,
    table_name,
    record_id,
    NULL,
    jsonb_build_object('deleted_at', NOW())
  );
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 7: Create Search Vector Update Function
-- =====================================================

CREATE OR REPLACE FUNCTION update_medical_records_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector := 
    setweight(to_tsvector('english', COALESCE(NEW.record_type, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(NEW.department, '')), 'B') ||
    setweight(to_tsvector('english', COALESCE(NEW.diagnoses::text, '')), 'C');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 8: Create Triggers
-- =====================================================

-- Update timestamp triggers
CREATE TRIGGER update_organizations_updated_at
  BEFORE UPDATE ON organizations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at
  BEFORE UPDATE ON documents
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_medical_records_updated_at
  BEFORE UPDATE ON medical_records
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Audit triggers
CREATE TRIGGER audit_organizations
  AFTER INSERT OR UPDATE OR DELETE ON organizations
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_documents
  AFTER INSERT OR UPDATE OR DELETE ON documents
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_medical_records
  AFTER INSERT OR UPDATE OR DELETE ON medical_records
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

-- Search vector trigger
CREATE TRIGGER update_medical_records_search_vector_trigger
  BEFORE INSERT OR UPDATE ON medical_records
  FOR EACH ROW
  EXECUTE FUNCTION update_medical_records_search_vector();

-- =====================================================
-- STEP 9: Enable Row Level Security
-- =====================================================

ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE encryption_keys ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 10: Create RLS Policies
-- =====================================================

-- Organizations Policies
CREATE POLICY "Users can view their organization"
  ON organizations FOR SELECT
  USING (
    id = get_user_organization(auth.uid())
    AND deleted_at IS NULL
  );

CREATE POLICY "Admins can update organization"
  ON organizations FOR UPDATE
  USING (
    id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'admin')
    AND deleted_at IS NULL
  );

-- User Profiles Policies
CREATE POLICY "Users can view profiles in organization"
  ON user_profiles FOR SELECT
  USING (
    organization_id = get_user_organization(auth.uid())
    AND deleted_at IS NULL
  );

CREATE POLICY "Users can update own profile"
  ON user_profiles FOR UPDATE
  USING (id = auth.uid())
  WITH CHECK (
    id = auth.uid() 
    AND organization_id = get_user_organization(auth.uid())
  );

CREATE POLICY "Admins can manage profiles"
  ON user_profiles FOR ALL
  USING (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'admin')
  );

-- Documents Policies
CREATE POLICY "Users can view documents"
  ON documents FOR SELECT
  USING (
    organization_id = get_user_organization(auth.uid())
    AND deleted_at IS NULL
  );

CREATE POLICY "Editors can create documents"
  ON documents FOR INSERT
  WITH CHECK (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'editor')
  );

CREATE POLICY "Editors can update documents"
  ON documents FOR UPDATE
  USING (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'editor')
    AND deleted_at IS NULL
  );

CREATE POLICY "Admins can delete documents"
  ON documents FOR DELETE
  USING (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'admin')
  );

-- Medical Records Policies
CREATE POLICY "Users can view medical records"
  ON medical_records FOR SELECT
  USING (
    organization_id = get_user_organization(auth.uid())
    AND deleted_at IS NULL
    AND (
      has_role(auth.uid(), 'editor')
      OR has_role(auth.uid(), 'viewer')
      OR has_role(auth.uid(), 'analyst')
    )
  );

CREATE POLICY "Editors can create medical records"
  ON medical_records FOR INSERT
  WITH CHECK (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'editor')
  );

CREATE POLICY "Editors can update medical records"
  ON medical_records FOR UPDATE
  USING (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'editor')
    AND deleted_at IS NULL
  );

-- Audit Logs Policies
CREATE POLICY "Admins can view audit logs"
  ON audit_logs FOR SELECT
  USING (
    organization_id = get_user_organization(auth.uid())
    AND has_role(auth.uid(), 'admin')
  );

-- Encryption Keys Policies
CREATE POLICY "System manages encryption keys"
  ON encryption_keys FOR ALL
  USING (false)
  WITH CHECK (false);

-- =====================================================
-- STEP 11: Create Secure View for Medical Records
-- =====================================================

CREATE OR REPLACE VIEW medical_records_secure AS
SELECT 
  id,
  document_id,
  organization_id,
  CASE 
    WHEN has_role(auth.uid(), 'editor') THEN 
      decrypt_pii(patient_name_encrypted, organization_id)
    ELSE NULL 
  END as patient_name,
  CASE 
    WHEN has_role(auth.uid(), 'editor') THEN 
      decrypt_pii(patient_id_encrypted, organization_id)
    ELSE NULL 
  END as patient_id,
  CASE 
    WHEN has_role(auth.uid(), 'editor') THEN 
      decrypt_pii(hospital_number_encrypted, organization_id)
    ELSE NULL 
  END as hospital_number,
  CASE 
    WHEN has_role(auth.uid(), 'editor') THEN 
      decrypt_pii(lab_number_encrypted, organization_id)
    ELSE NULL 
  END as lab_number,
  record_date,
  record_type,
  department,
  test_results,
  diagnoses,
  medications,
  confidence_score,
  extraction_metadata,
  created_at,
  updated_at,
  deleted_at
FROM medical_records
WHERE deleted_at IS NULL;

GRANT SELECT ON medical_records_secure TO authenticated;

-- =====================================================
-- STEP 12: Create Storage Buckets (Run in Supabase Dashboard)
-- =====================================================

-- Note: Storage bucket creation should be done via Supabase Dashboard
-- or using the Supabase client library, not SQL

-- =====================================================
-- STEP 13: Create Initial Data
-- =====================================================

-- Create default organization
INSERT INTO organizations (name) 
VALUES ('Default Organization')
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- STEP 14: Create Authentication Trigger
-- =====================================================

CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO user_profiles (id, organization_id, role)
  VALUES (
    NEW.id,
    COALESCE(
      (NEW.raw_user_meta_data->>'organization_id')::UUID,
      (SELECT id FROM organizations WHERE name = 'Default Organization' LIMIT 1)
    ),
    COALESCE(
      (NEW.raw_user_meta_data->>'role')::user_role,
      'viewer'::user_role
    )
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user();

-- =====================================================
-- STEP 15: Create Utility Functions for Testing
-- =====================================================

-- Function to create test encryption key for organization
CREATE OR REPLACE FUNCTION create_test_encryption_key(org_id UUID)
RETURNS void AS $$
BEGIN
  INSERT INTO encryption_keys (organization_id, key_name, encrypted_key, is_active)
  VALUES (org_id, 'primary', gen_random_bytes(32), true)
  ON CONFLICT (organization_id, key_name) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if all tables were created
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN (
    'organizations', 'user_profiles', 'documents', 
    'medical_records', 'audit_logs', 'encryption_keys'
  )
ORDER BY table_name;

-- Check if RLS is enabled
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN (
    'organizations', 'user_profiles', 'documents', 
    'medical_records', 'audit_logs', 'encryption_keys'
  );

-- Check policies
SELECT schemaname, tablename, policyname 
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;